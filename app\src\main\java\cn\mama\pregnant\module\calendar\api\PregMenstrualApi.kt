package cn.mama.pregnant.module.calendar.api

import cn.mama.pregnant.module.calendar.bean.CalendarToolList
import kotlinx.coroutines.flow.Flow
import network.response.BaseResponse
import network.rxokhttp.annotation.GET
import network.rxokhttp.annotation.POST

/**
 *  记经期api
 *
 * <AUTHOR>
 * @date 2021/11/5
 */
interface PregMenstrualApi {

    @GET
    fun checkOvulation(url: String): Flow<BaseResponse<Any>>

    @POST
    fun saveOvulation(url: String, map: Map<String, Any?>): Flow<BaseResponse<Any>>
    @GET
    fun getCalendarToolList(url: String) : Flow<BaseResponse<CalendarToolList>>
}