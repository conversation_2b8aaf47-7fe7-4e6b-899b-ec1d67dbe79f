package cn.mama.pregnant.activity

import android.content.Context
import android.content.Intent
import cn.mama.pregnant.business.account.bean.UserInfo
import cn.mama.pregnant.business.bean.home.HomeExtra
import cn.mama.pregnant.business.consts.HomeConstant
import cn.mama.pregnant.web.x5.util.WebLoginUtils

/**
 *  主页Intent管理
 *
 * <AUTHOR>
 * @date 2020/11/20
 */

/**
 * 主页Intent参数统一入口
 * 显示指定的[tabFlag]Tab位置，默认显示首页,目前支持的数值有
 *   首页[HomeConstant.TO_HOME_PAGE],
 *   日历[HomeConstant.TO_CALENDAR],
 *   陪伴[HomeConstant.TO_ACCOMPANY]
 *   社区[HomeConstant.TO_TOPIC]
 * [switchCalendar]true-发现工具点击日历后跳转日历,false-忽略该参数
 */
@JvmOverloads
fun createHomeIntent(
    ctx: Context,
    tabFlag: Int = HomeConstant.TO_HOME_PAGE,
    switchCalendar: Boolean = false,
    homeExtra: HomeExtra? = null
): Intent {
    val mama = UserInfo.instance().isMaMa
    val dad = UserInfo.instance().isBaba
    return Intent(ctx, if (mama) HomeActivity::class.java else if (dad) DadHomeActivity::class.java else FriendHomeActivity::class.java) .apply {
        putExtra(WebLoginUtils.GO_MENSTRUAL_CALENDAR, tabFlag)
        putExtra(HomeConstant.SWITCH_CALENDAR, switchCalendar)
        putExtra(HomeConstant.INTENT_HOME_EXTRA, homeExtra)
    }
}

/**
 * singleTask启动home,如果activity已经存在，不会重现创建Activity
 */
@JvmOverloads
fun launchHome(
    ctx: Context,
    tabFlag: Int = HomeConstant.TO_HOME_PAGE,
    switchCalendar: Boolean = false,
    homeExtra: HomeExtra? = null,
    isPush: Boolean = false
) {
    val homeIntent = createHomeIntent(ctx, tabFlag, switchCalendar, homeExtra).apply {
        addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        putExtra(HomeConstant.INTENT_HOME_PUSH, isPush)
    }
    ctx.startActivity(homeIntent)
}