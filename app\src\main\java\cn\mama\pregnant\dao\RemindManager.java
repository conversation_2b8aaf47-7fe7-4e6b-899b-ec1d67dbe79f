package cn.mama.pregnant.dao;

import android.content.Context;

/**
 * 提醒管理
 *
 * <AUTHOR>
 */
public class RemindManager {
    private static RemindDao mRemindDao;
    public static synchronized  RemindDao getRemindDao(Context context) {
        if (mRemindDao == null) {
            Context ctx = context.getApplicationContext();
            mRemindDao = new RemindDaoImpl(ctx);
        }
        return mRemindDao;
    }

}
