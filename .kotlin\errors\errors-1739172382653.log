kotlin version: 2.0.21
error message: org.jetbrains.kotlin.backend.common.BackendException: Backend Internal error: Exception during IR lowering
File being compiled: C:/E_Dev/AndroidProject/NewProject/pregnancy/module-hardware/src/main/java/cn/mama/hardware/thermometer/activity/ThermometerConnectActivity.kt
The root cause java.lang.RuntimeException was thrown at: org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:47)
	at org.jetbrains.kotlin.backend.common.CodegenUtil.reportBackendException(CodegenUtil.kt:253)
	at org.jetbrains.kotlin.backend.common.CodegenUtil.reportBackendException$default(CodegenUtil.kt:236)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invokeSequential(performByIrFile.kt:65)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invoke(performByIrFile.kt:52)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invoke(performByIrFile.kt:38)
	at org.jetbrains.kotlin.backend.common.phaser.NamedCompilerPhase.phaseBody(CompilerPhase.kt:166)
	at org.jetbrains.kotlin.backend.common.phaser.AbstractNamedCompilerPhase.invoke(CompilerPhase.kt:113)
	at org.jetbrains.kotlin.backend.common.phaser.CompositePhase.invoke(PhaseBuilders.kt:27)
	at org.jetbrains.kotlin.backend.common.phaser.CompositePhase.invoke(PhaseBuilders.kt:14)
	at org.jetbrains.kotlin.backend.common.phaser.NamedCompilerPhase.phaseBody(CompilerPhase.kt:166)
	at org.jetbrains.kotlin.backend.common.phaser.AbstractNamedCompilerPhase.invoke(CompilerPhase.kt:113)
	at org.jetbrains.kotlin.backend.common.phaser.CompilerPhaseKt.invokeToplevel(CompilerPhase.kt:62)
	at org.jetbrains.kotlin.backend.jvm.JvmIrCodegenFactory.invokeCodegen(JvmIrCodegenFactory.kt:371)
	at org.jetbrains.kotlin.codegen.CodegenFactory.generateModule(CodegenFactory.kt:47)
	at org.jetbrains.kotlin.backend.jvm.JvmIrCodegenFactory.generateModuleInFrontendIRMode(JvmIrCodegenFactory.kt:433)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.generateCodeFromIr(jvmCompilerPipeline.kt:246)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.compileModulesUsingFrontendIrAndLightTree(jvmCompilerPipeline.kt:142)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:148)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:43)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:103)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:49)
	at org.jetbrains.kotlin.cli.common.CLITool.exec(CLITool.kt:101)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:464)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:73)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:506)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:423)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally$lambda$9$compile(IncrementalCompilerRunner.kt:249)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally(IncrementalCompilerRunner.kt:267)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:120)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:675)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1660)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base/java.lang.reflect.Method.invoke(Unknown Source)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport$1.run(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport$1.run(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.RuntimeException: Exception while generating code for:
FUN name:ThermometerConnectScreen visibility:public modality:FINAL <> (viewModel:cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel) returnType:kotlin.Unit
  annotations:
    OptIn(markerClass = [CLASS_REFERENCE 'CLASS IR_EXTERNAL_DECLARATION_STUB ANNOTATION_CLASS name:ExperimentalMaterial3Api modality:OPEN visibility:public superTypes:[kotlin.Annotation]' type=kotlin.reflect.KClass<androidx.compose.material3.ExperimentalMaterial3Api>])
    Composable
  VALUE_PARAMETER name:viewModel index:0 type:cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel
  BLOCK_BODY
    VAR PROPERTY_DELEGATE name:eventRecordText$delegate type:androidx.compose.runtime.State<kotlin.String?> [val]
      TYPE_OP type=androidx.compose.runtime.State<kotlin.String?> origin=IMPLICIT_CAST typeOperand=androidx.compose.runtime.State<kotlin.String?>
        CALL 'public final fun collectAsState$default <T> (context: kotlin.coroutines.CoroutineContext?, $mask0: kotlin.Int, $handler: kotlin.Any?): androidx.compose.runtime.State<T of androidx.compose.runtime.SnapshotStateKt.collectAsState$default> declared in androidx.compose.runtime.SnapshotStateKt' type=androidx.compose.runtime.State<T of androidx.compose.runtime.SnapshotStateKt.collectAsState$default> origin=DEFAULT_DISPATCH_CALL
          <T>: kotlin.String?
          $receiver: CALL 'public final fun <get-eventRecordText> (): kotlinx.coroutines.flow.StateFlow<kotlin.String?> declared in cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel' type=kotlinx.coroutines.flow.StateFlow<kotlin.String?> origin=GET_PROPERTY
            $this: GET_VAR 'viewModel: cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen' type=cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel origin=null
          context: COMPOSITE type=kotlin.coroutines.CoroutineContext? origin=DEFAULT_VALUE
            CONST Null type=kotlin.coroutines.CoroutineContext? value=null
          $mask0: CONST Int type=kotlin.Int value=1
          $handler: CONST Null type=kotlin.Any? value=null
    VAR PROPERTY_DELEGATE name:highLowTempText$delegate type:androidx.compose.runtime.State<kotlin.String?> [val]
      TYPE_OP type=androidx.compose.runtime.State<kotlin.String?> origin=IMPLICIT_CAST typeOperand=androidx.compose.runtime.State<kotlin.String?>
        CALL 'public final fun collectAsState$default <T> (context: kotlin.coroutines.CoroutineContext?, $mask0: kotlin.Int, $handler: kotlin.Any?): androidx.compose.runtime.State<T of androidx.compose.runtime.SnapshotStateKt.collectAsState$default> declared in androidx.compose.runtime.SnapshotStateKt' type=androidx.compose.runtime.State<T of androidx.compose.runtime.SnapshotStateKt.collectAsState$default> origin=DEFAULT_DISPATCH_CALL
          <T>: kotlin.String?
          $receiver: CALL 'public final fun <get-highLowTempText> (): kotlinx.coroutines.flow.StateFlow<kotlin.String?> declared in cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel' type=kotlinx.coroutines.flow.StateFlow<kotlin.String?> origin=GET_PROPERTY
            $this: GET_VAR 'viewModel: cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen' type=cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel origin=null
          context: COMPOSITE type=kotlin.coroutines.CoroutineContext? origin=DEFAULT_VALUE
            CONST Null type=kotlin.coroutines.CoroutineContext? value=null
          $mask0: CONST Int type=kotlin.Int value=1
          $handler: CONST Null type=kotlin.Any? value=null
    VAR PROPERTY_DELEGATE name:purchaseLink$delegate type:androidx.compose.runtime.State<kotlin.String?> [val]
      TYPE_OP type=androidx.compose.runtime.State<kotlin.String?> origin=IMPLICIT_CAST typeOperand=androidx.compose.runtime.State<kotlin.String?>
        CALL 'public final fun collectAsState$default <T> (context: kotlin.coroutines.CoroutineContext?, $mask0: kotlin.Int, $handler: kotlin.Any?): androidx.compose.runtime.State<T of androidx.compose.runtime.SnapshotStateKt.collectAsState$default> declared in androidx.compose.runtime.SnapshotStateKt' type=androidx.compose.runtime.State<T of androidx.compose.runtime.SnapshotStateKt.collectAsState$default> origin=DEFAULT_DISPATCH_CALL
          <T>: kotlin.String?
          $receiver: CALL 'public final fun <get-purchaseLink> (): kotlinx.coroutines.flow.StateFlow<kotlin.String?> declared in cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel' type=kotlinx.coroutines.flow.StateFlow<kotlin.String?> origin=GET_PROPERTY
            $this: GET_VAR 'viewModel: cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen' type=cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel origin=null
          context: COMPOSITE type=kotlin.coroutines.CoroutineContext? origin=DEFAULT_VALUE
            CONST Null type=kotlin.coroutines.CoroutineContext? value=null
          $mask0: CONST Int type=kotlin.Int value=1
          $handler: CONST Null type=kotlin.Any? value=null
    CALL 'public final fun Column$default (modifier: androidx.compose.ui.Modifier?, verticalArrangement: androidx.compose.foundation.layout.Arrangement.Vertical?, horizontalAlignment: androidx.compose.ui.Alignment.Horizontal?, content: @[Composable] @[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.ColumnScope, kotlin.Unit>, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Unit [inline] declared in androidx.compose.foundation.layout.ColumnKt' type=kotlin.Unit origin=DEFAULT_DISPATCH_CALL
      modifier: CALL 'public final fun padding-3ABfNKs (all: androidx.compose.ui.unit.Dp): androidx.compose.ui.Modifier declared in androidx.compose.foundation.layout.PaddingKt' type=androidx.compose.ui.Modifier origin=null
        $receiver: CALL 'public final fun fillMaxSize$default (fraction: kotlin.Float, $mask0: kotlin.Int, $handler: kotlin.Any?): androidx.compose.ui.Modifier declared in androidx.compose.foundation.layout.SizeKt' type=androidx.compose.ui.Modifier origin=DEFAULT_DISPATCH_CALL
          $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.Modifier.Companion visibility:public [final,static] declared in androidx.compose.ui.Modifier' type=androidx.compose.ui.Modifier.Companion origin=null
          fraction: COMPOSITE type=kotlin.Float origin=DEFAULT_VALUE
            CONST Float type=kotlin.Float value=0.0
          $mask0: CONST Int type=kotlin.Int value=1
          $handler: CONST Null type=kotlin.Any? value=null
        all: CALL 'public final fun <get-dp> (): androidx.compose.ui.unit.Dp [inline] declared in androidx.compose.ui.unit.DpKt' type=androidx.compose.ui.unit.Dp origin=GET_PROPERTY
          $receiver: CONST Int type=kotlin.Int value=16
      verticalArrangement: COMPOSITE type=androidx.compose.foundation.layout.Arrangement.Vertical? origin=DEFAULT_VALUE
        CONST Null type=androidx.compose.foundation.layout.Arrangement.Vertical? value=null
      horizontalAlignment: COMPOSITE type=androidx.compose.ui.Alignment.Horizontal? origin=DEFAULT_VALUE
        CONST Null type=androidx.compose.ui.Alignment.Horizontal? value=null
      content: BLOCK type=@[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.ColumnScope, kotlin.Unit> origin=LAMBDA
        COMPOSITE type=kotlin.Unit origin=null
        FUNCTION_REFERENCE 'private final fun ThermometerConnectScreen$lambda$13 ($viewModel: cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel): kotlin.Unit declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt' type=@[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.ColumnScope, kotlin.Unit> origin=INLINE_LAMBDA reflectionTarget=null
          $viewModel: GET_VAR 'viewModel: cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen' type=cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel origin=null
      $mask0: CONST Int type=kotlin.Int value=6
      $handler: CONST Null type=kotlin.Any? value=null

	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:47)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate$default(FunctionCodegen.kt:40)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethodNode(ClassCodegen.kt:406)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethod(ClassCodegen.kt:423)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generate(ClassCodegen.kt:168)
	at org.jetbrains.kotlin.backend.jvm.FileCodegen.lower(JvmPhases.kt:39)
	at org.jetbrains.kotlin.backend.common.phaser.PhaseFactoriesKt.createFilePhase$lambda$4(PhaseFactories.kt:71)
	at org.jetbrains.kotlin.backend.common.phaser.PhaseBuildersKt$createSimpleNamedCompilerPhase$1.phaseBody(PhaseBuilders.kt:69)
	at org.jetbrains.kotlin.backend.common.phaser.SimpleNamedCompilerPhase.phaseBody(CompilerPhase.kt:226)
	at org.jetbrains.kotlin.backend.common.phaser.AbstractNamedCompilerPhase.invoke(CompilerPhase.kt:113)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invokeSequential(performByIrFile.kt:62)
	... 44 more
Caused by: java.lang.RuntimeException: Exception while generating code for:
FUN INLINE_LAMBDA name:ThermometerConnectScreen$lambda$13 visibility:private modality:FINAL <> ($receiver:androidx.compose.foundation.layout.ColumnScope, $viewModel:cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel) returnType:kotlin.Unit
  $receiver: VALUE_PARAMETER name:$this$Column type:androidx.compose.foundation.layout.ColumnScope
  VALUE_PARAMETER BOUND_VALUE_PARAMETER name:$viewModel index:0 type:cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel
  BLOCK_BODY
    VAR name:$i$a$-Column$default-ThermometerConnectActivityKt$ThermometerConnectScreen$1 type:kotlin.Int [val]
      CONST Int type=kotlin.Int value=0
    CALL 'public final fun TopBar (onBackPressed: kotlin.Function0<kotlin.Unit>): kotlin.Unit declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt' type=kotlin.Unit origin=null
      onBackPressed: BLOCK type=kotlin.Function0<kotlin.Unit> origin=LAMBDA
        COMPOSITE type=kotlin.Unit origin=null
        CALL 'public final fun <jvm-indy> <T> (dynamicCall: T of kotlin.jvm.internal.<jvm-indy>, bootstrapMethodHandle: kotlin.Any, vararg bootstrapMethodArguments: kotlin.Any): T of kotlin.jvm.internal.<jvm-indy> declared in kotlin.jvm.internal' type=kotlin.Function0<kotlin.Unit> origin=null
          <T>: kotlin.Function0<kotlin.Unit>
          dynamicCall: CALL 'public final fun invoke (): kotlin.Function0<kotlin.Unit> declared in kotlin.jvm.internal.invokeDynamic' type=kotlin.Function0<kotlin.Unit> origin=null
          bootstrapMethodHandle: CALL 'public final fun <jvm-method-handle> (tag: kotlin.Int, owner: kotlin.String, name: kotlin.String, descriptor: kotlin.String, isInterface: kotlin.Boolean): kotlin.Any declared in kotlin.jvm.internal' type=kotlin.Any origin=null
            tag: CONST Int type=kotlin.Int value=6
            owner: CONST String type=kotlin.String value="java/lang/invoke/LambdaMetafactory"
            name: CONST String type=kotlin.String value="metafactory"
            descriptor: CONST String type=kotlin.String value="(Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/invoke/MethodType;Ljava/lang/invoke/MethodType;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/CallSite;"
            isInterface: CONST Boolean type=kotlin.Boolean value=false
          bootstrapMethodArguments: VARARG type=kotlin.Array<kotlin.Any> varargElementType=kotlin.Any
            CALL 'public final fun <jvm-original-method-type> (method: kotlin.Any): kotlin.Any declared in kotlin.jvm.internal' type=kotlin.Any origin=null
              method: RAW_FUNCTION_REFERENCE 'public abstract fun invoke (): R of kotlin.Function0 [operator] declared in kotlin.Function0' type=kotlin.Any
            RAW_FUNCTION_REFERENCE 'private final fun ThermometerConnectScreen$lambda$13$lambda$3 (): kotlin.Unit? declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt' type=kotlin.Any
            CALL 'public final fun <jvm-original-method-type> (method: kotlin.Any): kotlin.Any declared in kotlin.jvm.internal' type=kotlin.Any origin=null
              method: RAW_FUNCTION_REFERENCE 'public abstract fun invoke (): kotlin.Unit? [fake_override,operator] declared in kotlin.jvm.internal.invokeDynamic.<fake>' type=kotlin.Any
    CALL 'public final fun Spacer (modifier: androidx.compose.ui.Modifier): kotlin.Unit declared in androidx.compose.foundation.layout.SpacerKt' type=kotlin.Unit origin=null
      modifier: CALL 'public final fun height-3ABfNKs (height: androidx.compose.ui.unit.Dp): androidx.compose.ui.Modifier declared in androidx.compose.foundation.layout.SizeKt' type=androidx.compose.ui.Modifier origin=null
        $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.Modifier.Companion visibility:public [final,static] declared in androidx.compose.ui.Modifier' type=androidx.compose.ui.Modifier.Companion origin=null
        height: CALL 'public final fun <get-dp> (): androidx.compose.ui.unit.Dp [inline] declared in androidx.compose.ui.unit.DpKt' type=androidx.compose.ui.unit.Dp origin=GET_PROPERTY
          $receiver: CONST Int type=kotlin.Int value=10
    BLOCK type=kotlin.Unit origin=ARGUMENTS_REORDERING_FOR_CALL
      VAR IR_TEMPORARY_VARIABLE name:tmp0_style type:androidx.compose.ui.text.TextStyle [val]
        CALL 'public final fun <get-titleMedium> (): androidx.compose.ui.text.TextStyle declared in androidx.compose.material3.Typography' type=androidx.compose.ui.text.TextStyle origin=GET_PROPERTY
          $this: CALL 'public final fun <get-typography> (): androidx.compose.material3.Typography declared in androidx.compose.material3.MaterialTheme' type=androidx.compose.material3.Typography origin=GET_PROPERTY
            $this: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:androidx.compose.material3.MaterialTheme visibility:public [final,static] declared in androidx.compose.material3.MaterialTheme' type=androidx.compose.material3.MaterialTheme origin=null
      VAR IR_TEMPORARY_VARIABLE name:tmp1_textAlign type:androidx.compose.ui.text.style.TextAlign [val]
        CALL 'public final fun getCenter-e0LSkKk (): androidx.compose.ui.text.style.TextAlign declared in androidx.compose.ui.text.style.TextAlign.Companion' type=androidx.compose.ui.text.style.TextAlign origin=GET_PROPERTY
          $this: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.text.style.TextAlign.Companion visibility:public [final,static] declared in androidx.compose.ui.text.style.TextAlign' type=androidx.compose.ui.text.style.TextAlign.Companion origin=null
      CALL 'public final fun Text-80ic1_w$default (text: kotlin.String, modifier: androidx.compose.ui.Modifier?, color: androidx.compose.ui.graphics.Color, fontSize: androidx.compose.ui.unit.TextUnit, fontStyle: androidx.compose.ui.text.font.FontStyle?, fontWeight: androidx.compose.ui.text.font.FontWeight?, fontFamily: androidx.compose.ui.text.font.FontFamily?, letterSpacing: androidx.compose.ui.unit.TextUnit, textDecoration: androidx.compose.ui.text.style.TextDecoration?, textAlign: androidx.compose.ui.text.style.TextAlign?, lineHeight: androidx.compose.ui.unit.TextUnit, overflow: androidx.compose.ui.text.style.TextOverflow, softWrap: kotlin.Boolean, maxLines: kotlin.Int, minLines: kotlin.Int, onTextLayout: kotlin.Function1<androidx.compose.ui.text.TextLayoutResult, kotlin.Unit>?, style: androidx.compose.ui.text.TextStyle?, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Unit declared in androidx.compose.material3.TextKt' type=kotlin.Unit origin=DEFAULT_DISPATCH_CALL
        text: CONST String type=kotlin.String value="体温贴未连接"
        modifier: COMPOSITE type=androidx.compose.ui.Modifier? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.Modifier? value=null
        color: COMPOSITE type=androidx.compose.ui.graphics.Color origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.graphics.Color origin=null
            <T>: kotlin.Long
            <R>: androidx.compose.ui.graphics.Color
            v: CONST Long type=kotlin.Long value=0
        fontSize: COMPOSITE type=androidx.compose.ui.unit.TextUnit origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.unit.TextUnit origin=null
            <T>: kotlin.Long
            <R>: androidx.compose.ui.unit.TextUnit
            v: CONST Long type=kotlin.Long value=0
        fontStyle: COMPOSITE type=androidx.compose.ui.text.font.FontStyle? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.text.font.FontStyle? value=null
        fontWeight: COMPOSITE type=androidx.compose.ui.text.font.FontWeight? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.text.font.FontWeight? value=null
        fontFamily: COMPOSITE type=androidx.compose.ui.text.font.FontFamily? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.text.font.FontFamily? value=null
        letterSpacing: COMPOSITE type=androidx.compose.ui.unit.TextUnit origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.unit.TextUnit origin=null
            <T>: kotlin.Long
            <R>: androidx.compose.ui.unit.TextUnit
            v: CONST Long type=kotlin.Long value=0
        textDecoration: COMPOSITE type=androidx.compose.ui.text.style.TextDecoration? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.text.style.TextDecoration? value=null
        textAlign: GET_VAR 'val tmp1_textAlign: androidx.compose.ui.text.style.TextAlign [val] declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen$lambda$13' type=androidx.compose.ui.text.style.TextAlign origin=null
        lineHeight: COMPOSITE type=androidx.compose.ui.unit.TextUnit origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.unit.TextUnit origin=null
            <T>: kotlin.Long
            <R>: androidx.compose.ui.unit.TextUnit
            v: CONST Long type=kotlin.Long value=0
        overflow: COMPOSITE type=androidx.compose.ui.text.style.TextOverflow origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.text.style.TextOverflow origin=null
            <T>: kotlin.Int
            <R>: androidx.compose.ui.text.style.TextOverflow
            v: CONST Int type=kotlin.Int value=0
        softWrap: COMPOSITE type=kotlin.Boolean origin=DEFAULT_VALUE
          CONST Boolean type=kotlin.Boolean value=false
        maxLines: COMPOSITE type=kotlin.Int origin=DEFAULT_VALUE
          CONST Int type=kotlin.Int value=0
        minLines: COMPOSITE type=kotlin.Int origin=DEFAULT_VALUE
          CONST Int type=kotlin.Int value=0
        onTextLayout: COMPOSITE type=kotlin.Function1<androidx.compose.ui.text.TextLayoutResult, kotlin.Unit>? origin=DEFAULT_VALUE
          CONST Null type=kotlin.Function1<androidx.compose.ui.text.TextLayoutResult, kotlin.Unit>? value=null
        style: GET_VAR 'val tmp0_style: androidx.compose.ui.text.TextStyle [val] declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen$lambda$13' type=androidx.compose.ui.text.TextStyle origin=null
        $mask0: CONST Int type=kotlin.Int value=65022
        $handler: CONST Null type=kotlin.Any? value=null
    CALL 'public final fun Image$default (painter: androidx.compose.ui.graphics.painter.Painter, contentDescription: kotlin.String?, modifier: androidx.compose.ui.Modifier?, alignment: androidx.compose.ui.Alignment?, contentScale: androidx.compose.ui.layout.ContentScale?, alpha: kotlin.Float, colorFilter: androidx.compose.ui.graphics.ColorFilter?, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Unit declared in androidx.compose.foundation.ImageKt' type=kotlin.Unit origin=DEFAULT_DISPATCH_CALL
      painter: CALL 'public final fun painterResource (id: kotlin.Int): androidx.compose.ui.graphics.painter.Painter declared in androidx.compose.ui.res.PainterResources_androidKt' type=androidx.compose.ui.graphics.painter.Painter origin=null
        id: GET_FIELD 'FIELD IR_EXTERNAL_JAVA_DECLARATION_STUB name:ic_thermometer_placeholder type:kotlin.Int visibility:public [static] declared in cn.mama.hardware.R.drawable' type=kotlin.Int superQualifierSymbol=cn.mama.hardware.R.drawable origin=null
      contentDescription: CONST Null type=kotlin.Nothing? value=null
      modifier: COMPOSITE type=androidx.compose.ui.Modifier? origin=DEFAULT_VALUE
        CONST Null type=androidx.compose.ui.Modifier? value=null
      alignment: COMPOSITE type=androidx.compose.ui.Alignment? origin=DEFAULT_VALUE
        CONST Null type=androidx.compose.ui.Alignment? value=null
      contentScale: COMPOSITE type=androidx.compose.ui.layout.ContentScale? origin=DEFAULT_VALUE
        CONST Null type=androidx.compose.ui.layout.ContentScale? value=null
      alpha: COMPOSITE type=kotlin.Float origin=DEFAULT_VALUE
        CONST Float type=kotlin.Float value=0.0
      colorFilter: COMPOSITE type=androidx.compose.ui.graphics.ColorFilter? origin=DEFAULT_VALUE
        CONST Null type=androidx.compose.ui.graphics.ColorFilter? value=null
      $mask0: CONST Int type=kotlin.Int value=124
      $handler: CONST Null type=kotlin.Any? value=null
    BLOCK type=kotlin.Unit origin=ARGUMENTS_REORDERING_FOR_CALL
      VAR IR_TEMPORARY_VARIABLE name:tmp2_style type:androidx.compose.ui.text.TextStyle [val]
        CALL 'public final fun <get-bodyMedium> (): androidx.compose.ui.text.TextStyle declared in androidx.compose.material3.Typography' type=androidx.compose.ui.text.TextStyle origin=GET_PROPERTY
          $this: CALL 'public final fun <get-typography> (): androidx.compose.material3.Typography declared in androidx.compose.material3.MaterialTheme' type=androidx.compose.material3.Typography origin=GET_PROPERTY
            $this: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:androidx.compose.material3.MaterialTheme visibility:public [final,static] declared in androidx.compose.material3.MaterialTheme' type=androidx.compose.material3.MaterialTheme origin=null
      VAR IR_TEMPORARY_VARIABLE name:tmp3_textAlign type:androidx.compose.ui.text.style.TextAlign [val]
        CALL 'public final fun getCenter-e0LSkKk (): androidx.compose.ui.text.style.TextAlign declared in androidx.compose.ui.text.style.TextAlign.Companion' type=androidx.compose.ui.text.style.TextAlign origin=GET_PROPERTY
          $this: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.text.style.TextAlign.Companion visibility:public [final,static] declared in androidx.compose.ui.text.style.TextAlign' type=androidx.compose.ui.text.style.TextAlign.Companion origin=null
      CALL 'public final fun Text-80ic1_w$default (text: kotlin.String, modifier: androidx.compose.ui.Modifier?, color: androidx.compose.ui.graphics.Color, fontSize: androidx.compose.ui.unit.TextUnit, fontStyle: androidx.compose.ui.text.font.FontStyle?, fontWeight: androidx.compose.ui.text.font.FontWeight?, fontFamily: androidx.compose.ui.text.font.FontFamily?, letterSpacing: androidx.compose.ui.unit.TextUnit, textDecoration: androidx.compose.ui.text.style.TextDecoration?, textAlign: androidx.compose.ui.text.style.TextAlign?, lineHeight: androidx.compose.ui.unit.TextUnit, overflow: androidx.compose.ui.text.style.TextOverflow, softWrap: kotlin.Boolean, maxLines: kotlin.Int, minLines: kotlin.Int, onTextLayout: kotlin.Function1<androidx.compose.ui.text.TextLayoutResult, kotlin.Unit>?, style: androidx.compose.ui.text.TextStyle?, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Unit declared in androidx.compose.material3.TextKt' type=kotlin.Unit origin=DEFAULT_DISPATCH_CALL
        text: CONST String type=kotlin.String value="取出体温贴，点击下方开始连接"
        modifier: COMPOSITE type=androidx.compose.ui.Modifier? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.Modifier? value=null
        color: COMPOSITE type=androidx.compose.ui.graphics.Color origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.graphics.Color origin=null
            <T>: kotlin.Long
            <R>: androidx.compose.ui.graphics.Color
            v: CONST Long type=kotlin.Long value=0
        fontSize: COMPOSITE type=androidx.compose.ui.unit.TextUnit origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.unit.TextUnit origin=null
            <T>: kotlin.Long
            <R>: androidx.compose.ui.unit.TextUnit
            v: CONST Long type=kotlin.Long value=0
        fontStyle: COMPOSITE type=androidx.compose.ui.text.font.FontStyle? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.text.font.FontStyle? value=null
        fontWeight: COMPOSITE type=androidx.compose.ui.text.font.FontWeight? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.text.font.FontWeight? value=null
        fontFamily: COMPOSITE type=androidx.compose.ui.text.font.FontFamily? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.text.font.FontFamily? value=null
        letterSpacing: COMPOSITE type=androidx.compose.ui.unit.TextUnit origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.unit.TextUnit origin=null
            <T>: kotlin.Long
            <R>: androidx.compose.ui.unit.TextUnit
            v: CONST Long type=kotlin.Long value=0
        textDecoration: COMPOSITE type=androidx.compose.ui.text.style.TextDecoration? origin=DEFAULT_VALUE
          CONST Null type=androidx.compose.ui.text.style.TextDecoration? value=null
        textAlign: GET_VAR 'val tmp3_textAlign: androidx.compose.ui.text.style.TextAlign [val] declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen$lambda$13' type=androidx.compose.ui.text.style.TextAlign origin=null
        lineHeight: COMPOSITE type=androidx.compose.ui.unit.TextUnit origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.unit.TextUnit origin=null
            <T>: kotlin.Long
            <R>: androidx.compose.ui.unit.TextUnit
            v: CONST Long type=kotlin.Long value=0
        overflow: COMPOSITE type=androidx.compose.ui.text.style.TextOverflow origin=DEFAULT_VALUE
          CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=androidx.compose.ui.text.style.TextOverflow origin=null
            <T>: kotlin.Int
            <R>: androidx.compose.ui.text.style.TextOverflow
            v: CONST Int type=kotlin.Int value=0
        softWrap: COMPOSITE type=kotlin.Boolean origin=DEFAULT_VALUE
          CONST Boolean type=kotlin.Boolean value=false
        maxLines: COMPOSITE type=kotlin.Int origin=DEFAULT_VALUE
          CONST Int type=kotlin.Int value=0
        minLines: COMPOSITE type=kotlin.Int origin=DEFAULT_VALUE
          CONST Int type=kotlin.Int value=0
        onTextLayout: COMPOSITE type=kotlin.Function1<androidx.compose.ui.text.TextLayoutResult, kotlin.Unit>? origin=DEFAULT_VALUE
          CONST Null type=kotlin.Function1<androidx.compose.ui.text.TextLayoutResult, kotlin.Unit>? value=null
        style: GET_VAR 'val tmp2_style: androidx.compose.ui.text.TextStyle [val] declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen$lambda$13' type=androidx.compose.ui.text.TextStyle origin=null
        $mask0: CONST Int type=kotlin.Int value=65022
        $handler: CONST Null type=kotlin.Any? value=null
    CALL 'public final fun Spacer (modifier: androidx.compose.ui.Modifier): kotlin.Unit declared in androidx.compose.foundation.layout.SpacerKt' type=kotlin.Unit origin=null
      modifier: CALL 'public final fun height-3ABfNKs (height: androidx.compose.ui.unit.Dp): androidx.compose.ui.Modifier declared in androidx.compose.foundation.layout.SizeKt' type=androidx.compose.ui.Modifier origin=null
        $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.Modifier.Companion visibility:public [final,static] declared in androidx.compose.ui.Modifier' type=androidx.compose.ui.Modifier.Companion origin=null
        height: CALL 'public final fun <get-dp> (): androidx.compose.ui.unit.Dp [inline] declared in androidx.compose.ui.unit.DpKt' type=androidx.compose.ui.unit.Dp origin=GET_PROPERTY
          $receiver: CONST Int type=kotlin.Int value=14
    CALL 'public final fun Row$default (modifier: androidx.compose.ui.Modifier?, horizontalArrangement: androidx.compose.foundation.layout.Arrangement.Horizontal?, verticalAlignment: androidx.compose.ui.Alignment.Vertical?, content: @[Composable] @[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.RowScope, kotlin.Unit>, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Unit [inline] declared in androidx.compose.foundation.layout.RowKt' type=kotlin.Unit origin=DEFAULT_DISPATCH_CALL
      modifier: CALL 'public final fun fillMaxWidth$default (fraction: kotlin.Float, $mask0: kotlin.Int, $handler: kotlin.Any?): androidx.compose.ui.Modifier declared in androidx.compose.foundation.layout.SizeKt' type=androidx.compose.ui.Modifier origin=DEFAULT_DISPATCH_CALL
        $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.Modifier.Companion visibility:public [final,static] declared in androidx.compose.ui.Modifier' type=androidx.compose.ui.Modifier.Companion origin=null
        fraction: COMPOSITE type=kotlin.Float origin=DEFAULT_VALUE
          CONST Float type=kotlin.Float value=0.0
        $mask0: CONST Int type=kotlin.Int value=1
        $handler: CONST Null type=kotlin.Any? value=null
      horizontalArrangement: CALL 'public final fun <get-SpaceBetween> (): androidx.compose.foundation.layout.Arrangement.HorizontalOrVertical declared in androidx.compose.foundation.layout.Arrangement' type=androidx.compose.foundation.layout.Arrangement.HorizontalOrVertical origin=GET_PROPERTY
        $this: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:androidx.compose.foundation.layout.Arrangement visibility:public [final,static] declared in androidx.compose.foundation.layout.Arrangement' type=androidx.compose.foundation.layout.Arrangement origin=null
      verticalAlignment: COMPOSITE type=androidx.compose.ui.Alignment.Vertical? origin=DEFAULT_VALUE
        CONST Null type=androidx.compose.ui.Alignment.Vertical? value=null
      content: BLOCK type=@[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.RowScope, kotlin.Unit> origin=LAMBDA
        COMPOSITE type=kotlin.Unit origin=null
        FUNCTION_REFERENCE 'private final fun ThermometerConnectScreen$lambda$13$lambda$8 (): kotlin.Unit declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt' type=@[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.RowScope, kotlin.Unit> origin=INLINE_LAMBDA reflectionTarget=null
      $mask0: CONST Int type=kotlin.Int value=4
      $handler: CONST Null type=kotlin.Any? value=null
    CALL 'public final fun Spacer (modifier: androidx.compose.ui.Modifier): kotlin.Unit declared in androidx.compose.foundation.layout.SpacerKt' type=kotlin.Unit origin=null
      modifier: CALL 'public final fun height-3ABfNKs (height: androidx.compose.ui.unit.Dp): androidx.compose.ui.Modifier declared in androidx.compose.foundation.layout.SizeKt' type=androidx.compose.ui.Modifier origin=null
        $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.Modifier.Companion visibility:public [final,static] declared in androidx.compose.ui.Modifier' type=androidx.compose.ui.Modifier.Companion origin=null
        height: CALL 'public final fun <get-dp> (): androidx.compose.ui.unit.Dp [inline] declared in androidx.compose.ui.unit.DpKt' type=androidx.compose.ui.unit.Dp origin=GET_PROPERTY
          $receiver: CONST Int type=kotlin.Int value=20
    CALL 'public final fun GradeSelection (viewModel: cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel): kotlin.Unit declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt' type=kotlin.Unit origin=null
      viewModel: GET_VAR '$viewModel: cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt.ThermometerConnectScreen$lambda$13' type=cn.mama.hardware.thermometer.viewmodel.ThermometerConnectViewModel origin=null
    CALL 'public final fun Spacer (modifier: androidx.compose.ui.Modifier): kotlin.Unit declared in androidx.compose.foundation.layout.SpacerKt' type=kotlin.Unit origin=null
      modifier: CALL 'public final fun height-3ABfNKs (height: androidx.compose.ui.unit.Dp): androidx.compose.ui.Modifier declared in androidx.compose.foundation.layout.SizeKt' type=androidx.compose.ui.Modifier origin=null
        $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.Modifier.Companion visibility:public [final,static] declared in androidx.compose.ui.Modifier' type=androidx.compose.ui.Modifier.Companion origin=null
        height: CALL 'public final fun <get-dp> (): androidx.compose.ui.unit.Dp [inline] declared in androidx.compose.ui.unit.DpKt' type=androidx.compose.ui.unit.Dp origin=GET_PROPERTY
          $receiver: CONST Int type=kotlin.Int value=20
    CALL 'public final fun Row$default (modifier: androidx.compose.ui.Modifier?, horizontalArrangement: androidx.compose.foundation.layout.Arrangement.Horizontal?, verticalAlignment: androidx.compose.ui.Alignment.Vertical?, content: @[Composable] @[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.RowScope, kotlin.Unit>, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Unit [inline] declared in androidx.compose.foundation.layout.RowKt' type=kotlin.Unit origin=DEFAULT_DISPATCH_CALL
      modifier: CALL 'public final fun fillMaxWidth$default (fraction: kotlin.Float, $mask0: kotlin.Int, $handler: kotlin.Any?): androidx.compose.ui.Modifier declared in androidx.compose.foundation.layout.SizeKt' type=androidx.compose.ui.Modifier origin=DEFAULT_DISPATCH_CALL
        $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Companion type:androidx.compose.ui.Modifier.Companion visibility:public [final,static] declared in androidx.compose.ui.Modifier' type=androidx.compose.ui.Modifier.Companion origin=null
        fraction: COMPOSITE type=kotlin.Float origin=DEFAULT_VALUE
          CONST Float type=kotlin.Float value=0.0
        $mask0: CONST Int type=kotlin.Int value=1
        $handler: CONST Null type=kotlin.Any? value=null
      horizontalArrangement: CALL 'public final fun <get-SpaceEvenly> (): androidx.compose.foundation.layout.Arrangement.HorizontalOrVertical declared in androidx.compose.foundation.layout.Arrangement' type=androidx.compose.foundation.layout.Arrangement.HorizontalOrVertical origin=GET_PROPERTY
        $this: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:androidx.compose.foundation.layout.Arrangement visibility:public [final,static] declared in androidx.compose.foundation.layout.Arrangement' type=androidx.compose.foundation.layout.Arrangement origin=null
      verticalAlignment: COMPOSITE type=androidx.compose.ui.Alignment.Vertical? origin=DEFAULT_VALUE
        CONST Null type=androidx.compose.ui.Alignment.Vertical? value=null
      content: BLOCK type=@[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.RowScope, kotlin.Unit> origin=LAMBDA
        COMPOSITE type=kotlin.Unit origin=null
        FUNCTION_REFERENCE 'private final fun ThermometerConnectScreen$lambda$13$lambda$12 (): kotlin.Unit declared in cn.mama.hardware.thermometer.activity.ThermometerConnectActivityKt' type=@[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.RowScope, kotlin.Unit> origin=INLINE_LAMBDA reflectionTarget=null
      $mask0: CONST Int type=kotlin.Int value=4
      $handler: CONST Null type=kotlin.Any? value=null

	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:47)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrSourceCompilerForInline.generateLambdaBody(IrSourceCompilerForInline.kt:67)
	at org.jetbrains.kotlin.codegen.inline.ExpressionLambda.generateLambdaBody(LambdaInfo.kt:72)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrInlineCodegen.genValueAndPut(IrInlineCodegen.kt:100)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall$handleParameter(ExpressionCodegen.kt:616)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:644)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:579)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:584)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrBlockBody.accept(IrBlockBody.kt:20)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.generate(ExpressionCodegen.kt:240)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.doGenerate(FunctionCodegen.kt:123)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:44)
	... 54 more
Caused by: org.jetbrains.kotlin.codegen.CompilationException: Back-end (JVM) Internal error: Couldn't inline method call: CALL 'public final fun Row$default (modifier: androidx.compose.ui.Modifier?, horizontalArrangement: androidx.compose.foundation.layout.Arrangement.Horizontal?, verticalAlignment: androidx.compose.ui.Alignment.Vertical?, content: @[Composable] @[ExtensionFunctionType] kotlin.Function1<androidx.compose.foundation.layout.RowScope, kotlin.Unit>, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Unit [inline] declared in androidx.compose.foundation.layout.RowKt' type=kotlin.Unit origin=DEFAULT_DISPATCH_CALL
Method: null
File is unknown
The root cause java.lang.IllegalStateException was thrown at: org.jetbrains.kotlin.codegen.inline.SourceCompilerForInlineKt.getMethodNode(SourceCompilerForInline.kt:118)
	at org.jetbrains.kotlin.codegen.inline.InlineCodegen.performInline(InlineCodegen.kt:65)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrInlineCodegen.genInlineCall(IrInlineCodegen.kt:163)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrInlineCallGenerator.genCall(IrInlineCallGenerator.kt:36)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:653)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:579)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:584)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrBlockBody.accept(IrBlockBody.kt:20)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.generate(ExpressionCodegen.kt:240)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.doGenerate(FunctionCodegen.kt:123)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:44)
	... 68 more
Caused by: java.lang.IllegalStateException: couldn't find inline method Landroidx/compose/foundation/layout/RowKt;.Row$default(Landroidx/compose/ui/Modifier;Landroidx/compose/foundation/layout/Arrangement$Horizontal;Landroidx/compose/ui/Alignment$Vertical;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
	at org.jetbrains.kotlin.codegen.inline.SourceCompilerForInlineKt.getMethodNode(SourceCompilerForInline.kt:118)
	at org.jetbrains.kotlin.codegen.inline.SourceCompilerForInlineKt.loadCompiledInlineFunction(SourceCompilerForInline.kt:96)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrSourceCompilerForInline.compileInlineFunction(IrSourceCompilerForInline.kt:91)
	at org.jetbrains.kotlin.codegen.inline.InlineCodegen.compileInline(InlineCodegen.kt:43)
	at org.jetbrains.kotlin.codegen.inline.InlineCodegen.performInline(InlineCodegen.kt:51)
	... 80 more


