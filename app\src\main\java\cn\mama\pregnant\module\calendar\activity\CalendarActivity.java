package cn.mama.pregnant.module.calendar.activity;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import cn.mama.pregnant.business.base.SingleFragmentActivity;
import cn.mama.pregnant.business.consts.CalendarAlertEnum;
import cn.mama.pregnant.business.permission.UserRoleCheckUtil;
import cn.mama.pregnant.business.util.IntentExpandKt;
import cn.mama.pregnant.module.calendar.fragment.CalendarFragment;

/**
 * <AUTHOR>
 * @date 2019/8/7
 */
public class CalendarActivity extends SingleFragmentActivity {

    public static final String INTENT_ALERT = "INTENT_ALERT";

    @Override
    protected void init(Bundle savedInstanceState) {
        if (UserRoleCheckUtil.checkRoleIsFather(this, "记经期")) {
            return;
        }
        super.init(savedInstanceState);
        useStatusBarFontIconDark(true);
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        String fragmentsTag = "Android:support:fragments";
        outState.remove(fragmentsTag);
    }

    @Override
    protected Fragment newFragment() {
        CalendarAlertEnum alertEnum =
            IntentExpandKt.serializable(getIntent(), INTENT_ALERT, CalendarAlertEnum.class);
        if(alertEnum == null){
            alertEnum = CalendarAlertEnum.NONE;
        }
        return CalendarFragment.newInstance(true, alertEnum);
    }
}
