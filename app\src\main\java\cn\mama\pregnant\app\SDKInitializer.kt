package cn.mama.pregnant.app

import android.app.Application
import android.content.Context
import android.text.TextUtils
import androidx.startup.Initializer
import cn.jzvd.JZUtils
import cn.mama.MyApplication
import cn.mama.adsdk.ADUtils
import cn.mama.channel.AppTool
import cn.mama.libimageloader.ImageLoader.Companion.instance
import cn.mama.pregnant.business.ad.AdSpUtil
import cn.mama.pregnant.business.app.SDKManager
import cn.mama.pregnant.business.router.RouteServiceManager
import cn.mama.pregnant.business.util.LogUtil
import cn.mama.pregnant.clippingvideo.tx.TXUGCUtil
import cn.mama.pregnant.tencentim.base.ImActivityLifeCycleCallBack
import com.blankj.utilcode.util.Utils
import com.bytedance.hume.readapk.HumeSDK
import com.lansosdk.videoeditor.LanSoEditor
import com.meituan.android.walle.WalleChannelReader
import com.tencent.smtt.export.external.TbsCoreSettings
import com.tencent.smtt.sdk.QbSdk
 import com.umeng.commonsdk.UMConfigure
import image.helper.GlideLoader
import util.richtext.RichText

/**
 * 用于处理初始化第三方SDK
 *
 * <AUTHOR> 2021/10/22
 * */
class SDKInitializer : Initializer<Unit> {

    override fun create(context: Context) {
        initPlatformId(context)
        SDKManager.init(context,MyApplication.getAppContext())
        //蓝松初始化SDK
        LanSoEditor.initSDK(MyApplication.getAppContext())
        TXUGCUtil.Companion.INSTANCE.initTXUGCBase(MyApplication.getAppContext())
        initTTAD(context)
        initActivityLifeCallBack()
//        registerActivityLifecycleCallbacks(AppUtil.ACTIVITY_LIFECYCLE)
        initX5()
        SdkDelayedManager.initOther(MyApplication.getAppContext())
        Utils.init(MyApplication.getAppContext())
        instance.setImageLoader(GlideLoader())
        RichText.initCacheDir(context)
        JZUtils.SAVE_PROGRESS = 3000L
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }

    private fun initX5() {
        val map: HashMap<String, Any> = HashMap()
        map[TbsCoreSettings.TBS_SETTINGS_USE_SPEEDY_CLASSLOADER] = true
        map[TbsCoreSettings.TBS_SETTINGS_USE_DEXLOADER_SERVICE] = true
        QbSdk.initTbsSettings(map)
        //x5内核初始化接口
        val cb =  object :QbSdk.PreInitCallback{

               override fun onCoreInitFinished() {
                  LogUtil.e("QbSdk:onCoreInitFinished")
               }

               override fun onViewInitFinished(p0: Boolean) {
                   LogUtil.e("QbSdk:onViewInitFinished${p0}")
               }
           }
        //x5内核初始化接口
        QbSdk.initX5Environment(MyApplication.getAppContext(), cb)
    }

    private fun initPlatformId(context: Context) {
        MyApplication.platform_id = WalleChannelReader.getChannel(context)
        if (TextUtils.isEmpty(MyApplication.platform_id)) {
            MyApplication.platform_id = AppTool.getChannel(context)
        }
        val channel = HumeSDK.getChannel(context)
        if (!TextUtils.isEmpty(channel)) {
            MyApplication.platform_id = channel
        }
        //禁止采集信息
        UMConfigure.enableImsiCollection(false)
        UMConfigure.enableIccidCollection(false)
        UMConfigure.enableImeiCollection(false)
        UMConfigure.enableWiFiMacCollection(false)
        UMConfigure.preInit(
            context,
            context.resources.getString(cn.mama.pregnant.business.R.string.umeng_appkey),
            MyApplication.platform_id
        )
    }

    /**
     * 初始化穿山甲
     */
    private fun initTTAD(context: Context) {
        ADUtils.INSTANCE
            .initAdSdk(context)
            .openDebug(false)
//       if(AdSpUtil.getInstance().getIntValue(AdSpUtil.OPEN_PANGOLIN) == 1){//当穿山甲开关为开时初始化
//           val iPangleAdProvider  = RouteServiceManager.getInstance().iPangleAdProvider
//           iPangleAdProvider?.initTTAdManager(context)
//       }
    }

    private fun initActivityLifeCallBack() {
        registerActivityLifecycleCallbacks(ImActivityLifeCycleCallBack())
    }

    private fun registerActivityLifecycleCallbacks(callback: Application.ActivityLifecycleCallbacks?) {
        MyApplication.getAppContext().registerActivityLifecycleCallbacks(callback)
    }

}