package cn.mama.pregnant.module.calendar.api;

import cn.mama.pregnant.business.bean.EmptyBean;
import cn.mama.pregnant.business.bean.calendar.CalendarDataListBean;
import cn.mama.pregnant.business.bean.calendar.CalendarToolDataBean;
import cn.mama.pregnant.business.bean.calendar.MakeLoveBean;
import cn.mama.pregnant.business.bean.calendar.MakeLoveTips;
import cn.mama.pregnant.business.bean.calendar.MenstrualBean;
import cn.mama.pregnant.business.bean.calendar.MoodBean;
import cn.mama.pregnant.business.bean.calendar.PercentBean;
import cn.mama.pregnant.business.bean.calendar.SexualWhitesBean;
import cn.mama.pregnant.module.calendar.bean.TipsData;
import cn.mama.pregnant.module.calendar.bean.MenstrualTotalRecord;
import cn.mama.pregnancy.community.bean.PostDialogSummary;
import io.reactivex.Observable;
import java.util.Map;
import network.response.BaseResponse;
import network.rxokhttp.annotation.GET;
import network.rxokhttp.annotation.POST;

/**
 * 日历相关接口
 * author : echoMu
 * date   : 2019/3/14
 * desc   :
 * <AUTHOR>
 */
public interface CalendarRequestApi {

    @GET
    Observable<BaseResponse<TipsData>> getTipsData(String url);//获取日历提示信息

    @GET
    Observable<BaseResponse<MenstrualBean>> getCalendarMenstrualListByMonth(String url);//备孕日历当月大姨妈数据

    @GET
    Observable<BaseResponse<CalendarDataListBean>> getCalendarListByMonth(String url);//备孕日历当月备孕工具数据


    @GET
    Observable<BaseResponse<CalendarToolDataBean>> getCalendarToolData(String url);//获取备孕工具基础数据

    @POST
    Observable<BaseResponse<MenstrualBean>> changeMenstrualHome(String url, Map<String, Object> map);//保存首页大姨妈开关

    @POST
    Observable<BaseResponse<MakeLoveBean>> saveMakeLove(String url, Map<String, Object> map);//保存同房

    @POST
    Observable<BaseResponse<MoodBean>> saveMood(String url, Map<String, Object> map);//保存心情


    @POST
    Observable<BaseResponse<SexualWhitesBean>> saveWhites(String url, Map<String, Object> map);//保存体温

    @POST
    Observable<BaseResponse<EmptyBean>> saveFolicAcid(String url, Map<String, Object> map);//记录叶酸

    /**
     * 记经期-汇总记录
     */
    @GET
    Observable<BaseResponse<MenstrualTotalRecord>> getMenstrualTotalRecord(String url);

    @GET
    Observable<BaseResponse<PercentBean>> getPregnancyPercent(String url);

    @GET
    Observable<BaseResponse<MakeLoveTips>> getMakeLoveTips(String url);

    /**
     * 【12.6.0】获取备孕记经期帖子弹窗
     */
    @GET
    Observable<BaseResponse<PostDialogSummary>> getMenstrualPostDialog(String url);

}
