package cn.mama.pregnant.app.migration

import android.content.Context
import util.RunUtil

/**
 * 版本数据迁移基类
 *
 * <AUTHOR>
 * @date 2020/9/7
 */
abstract class AbstractMigration(protected val context: Context) {

    /**
     * 全局上下文
     */
    protected val appContext: Context = context.applicationContext

    /**
     * [oldVersion]上一个版本号,等于[VERSION_FIRST_USE]时为全新的应用;[currentVersion]当前版本号,
     * 耗时任务请启动异步线程处理
     */
    abstract fun migrate(oldVersion: Int, currentVersion: Int)

    /**
     * 判读是否是第一次使用app, true表示第一次使用app
     */
    protected fun isFirstUse(version: Int) = version == VERSION_FIRST_USE

    /**
     * 异步执行任务
     */
    protected fun async(run: () -> Unit) {
        RunUtil.getThreadPool().execute(run)
    }

    companion object {
        /**
         * 全新应用
         */
        const val VERSION_FIRST_USE = 0

        /**
         * 10.0.0版本VersionCode
         */
        const val VERSION_10_0_0 = 119
    }
}