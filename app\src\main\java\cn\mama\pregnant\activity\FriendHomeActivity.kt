package cn.mama.pregnant.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import androidx.fragment.app.Fragment
import base.mvp.factory.CreatePresenter
import cn.mama.pregnancy.community.fragment.topic.NewTopicFragment
import cn.mama.pregnant.R
import cn.mama.pregnant.business.account.bean.UserInfo
import cn.mama.pregnant.business.bean.home.BottomMenu
import cn.mama.pregnant.business.bean.home.HomeExtra
import cn.mama.pregnant.business.consts.HomeConstant
import cn.mama.pregnant.business.consts.IGlobalRouteProviderConsts
import cn.mama.pregnant.business.event.HomePageChangedEvent
import cn.mama.pregnant.business.router.RouteServiceManager
import cn.mama.pregnant.business.util.AppUtil
import cn.mama.pregnant.business.util.bottom.BottomMenuType
import cn.mama.pregnant.business.view.tab.TabButton
import cn.mama.pregnant.module.calendar.fragment.CalendarFragment
import cn.mama.pregnant.module.discovery.DiscoveryFragment
import cn.mama.pregnant.module.home.activity.BaseHomeActivity
import cn.mama.pregnant.module.home.contract.DaHomeContract
import cn.mama.pregnant.module.home.fragment.EmtryFragment
import cn.mama.pregnant.module.home.fragment.HomeFragment
import cn.mama.pregnant.module.home.interfaces.IHomeFragment
import cn.mama.pregnant.module.home.presenter.DaHomePresenter
import cn.mama.pregnant.module.mine.fragment.FriendIdentityMineFragment
import cn.mama.pregnant.module.mine.fragment.FriendIdentityMineFragment.Companion.newInstance
import cn.mama.pregnant.web.x5.util.WebLoginUtils
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ActivityUtils
import com.mama.babyrecord.mouble.main.fragment.BabyRecordFragment
import com.mama.babyrecord.mouble.main.fragment.BabyRecordFragment.Companion.newInstance
import com.mama.babyrecord.widget.RecordSelectDialog

/**
 * 亲友首页
 */
@Route(path = IGlobalRouteProviderConsts.HOME_FRIEND_PATH)
@CreatePresenter(DaHomePresenter::class)
class FriendHomeActivity : BaseHomeActivity<DaHomeContract.View, DaHomeContract.Presenter>(),
    DaHomeContract.View {
    private var mTabHome: TabButton? = null
    private var mTabDiscover: TabButton? = null
    private var mTabRecord: TabButton? = null
    private var mTabTreebee: TabButton? = null
    private var mTabMine: TabButton? = null

    private var dialog: RecordSelectDialog? = null

    private var mDiscoveryFragment: DiscoveryFragment? = null

    private var mCalendarFragment: CalendarFragment? = null
    private var mBabyRecordFragment: BabyRecordFragment? = null
    private var mFriendIdentityMineFragment: FriendIdentityMineFragment? = null

    /**
     * 单例模式启动activity
     * ps:该方法因为FLAG_ACTIVITY_CLEAR_TASK标记，所以activity不会复用，会重新创建
     *
     * @param ctx
     */
    operator fun invoke(ctx: Context) {
        val i = createHomeIntent(ctx)
        i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        ctx.startActivity(i)
    }

    @SuppressLint("CheckResult")
    override fun onCreate(savedInstanceState: Bundle?) {
        savedInstanceState?.putParcelable("android:support:fragments", null)
        super.onCreate(savedInstanceState)
        if (UserInfo.instance().isMaMa || UserInfo.instance().isBaba) {
            invoke(this)
            finish()
            return
        }
        presenter.onCreate(this, intent)
        context = this
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_dad_home
    }

    override fun init(savedInstanceState: Bundle?) {
        super.init(savedInstanceState)
        initView()
        initData()
        initFragment()
        resettingTabButton()
        updateFragment(false, homeExtra)
        initBottomIcon()
        getTag(true, true)
        val isAlive = AppUtil.isHomeActivityAlive()
        Log.e("BaseHomeActivity", "=$isAlive")
    }

    override fun initView() {
        super.initView()
        mTabHome = findViewById(R.id.homepage)
        mTabDiscover = findViewById(R.id.discover)
        mTabRecord = findViewById(R.id.record)
        mTabTreebee = findViewById(R.id.treebee)
        mTabMine = findViewById(R.id.mine)
    }

    override fun initData() {
        super.initData()
        mTabsOriginal = ArrayList()
        mTabsOriginal.add(mTabHome)
        mTabsOriginal.add(mTabTreebee)
        mTabsOriginal.add(mTabRecord)
        mTabsOriginal.add(mTabDiscover)
        mTabsOriginal.add(mTabMine)
    }

    override fun onCustomNewIntent(intent: Intent?) {
        super.onCustomNewIntent(intent)
        if (presenter != null) {
            presenter.onCustomNewIntent(this, intent)
        }
    }


    private fun toRecord(): Boolean {
        if (UserInfo.instance().isLogin) { //登录状态
            if (!TextUtils.isEmpty(UserInfo.instance().getmTUid())) { //TUID不为空，关联成功的状态
                switchFragment(1)
            } else { //TUID为空，没有关联的状态
                //tabContainer.check(mLsatCheckedId);
                switchFragment(currentTabIndex)
                if (!ActivityUtils.isActivityAlive(this)) {
                    return true
                }
                if (null == dialog) {
                    dialog = RecordSelectDialog(this)
                }
                dialog?.initDialog()
            }
        } else { //未登录状态
            gotoLogin()
            //tabContainer.check(mLsatCheckedId);
            switchFragment(currentTabIndex)
        }
        return false
    }


    override fun setupInitialise() {
        super.setupInitialise()
        mDiscoveryFragment = null

    }


    override fun switchFirstChoose(bottomMenus: List<BottomMenu>?) {
        var tabIndex = intent.getIntExtra(WebLoginUtils.GO_MENSTRUAL_CALENDAR, 0)
        if (bottomMenus != null) {
            for (i in bottomMenus.indices) {
                val bottomMenu = bottomMenus[i]
                if (bottomMenu.selected == 1) {
                    homeTabIndex = i
                    tabIndex = getIndexInParenting(mBottomMenus, bottomMenu.type)
                    break
                    //如果是-1 当做是跳转到switchFragment(getIndex_Home())
                }
            }
        }
        switchFragmentByHomeExtra(tabIndex, homeExtra)
    }

    /**
     * 通过type获取fragment
     *
     * @param type
     * @param bottomMenu
     * @return
     */
    override fun getFragmentByType(type: String, bottomMenu: BottomMenu): Fragment? {
        if (TextUtils.isEmpty(type)) return null

        when (type) {
            BottomMenuType.HOME -> {
                return mHomeFragment
            }

            BottomMenuType.WAP -> {
                return EmtryFragment()
            }

            BottomMenuType.TOOL -> {
                return mDiscoveryFragment
            }

            BottomMenuType.MALL -> {
                return EmtryFragment()
            }

            BottomMenuType.CALENDAR -> {
                return mCalendarFragment
            }

            BottomMenuType.THREAD -> {
                return mTopicFragment
            }

            BottomMenuType.XJ -> {
                return mBabyRecordFragment
            }

            BottomMenuType.CENTER -> {
                return mFriendIdentityMineFragment
            }
        }
        return null

    }

    override fun getDynamicHomeIndex(): Int {
        return if (homeTabIndex != -1) {
            homeTabIndex
        } else super.getDynamicHomeIndex()
    }


    /**
     * 初始化fragment
     * 育儿，有学院没记录
     * 怀孕，有记录没学院
     */
    override fun initFragment() {
        super.initFragment()

        mHomeFragment = HomeFragment.newInstance()

        //发现
        mDiscoveryFragment = DiscoveryFragment.newInstance()
        //圈子
        mTopicFragment = NewTopicFragment.newInstance()
        //日历
        mCalendarFragment = CalendarFragment.newInstance(false)
        //小记信息流
        mBabyRecordFragment = newInstance(1, UserInfo.instance().bBid)
        //亲友版我的
        mFriendIdentityMineFragment = newInstance()
    }

    override fun getHomeMode(): Boolean {
        return UserInfo.instance().isFriend
    }

    fun onEventMainThread(event: HomePageChangedEvent) {
        onHomePageChangedEventd(event)
        synchronized(this) {
            when (event.index) {
                HomeConstant.TO_FRIEND_MINE -> {
                    //个人中心
                    if (isShowType(FriendIdentityMineFragment::class.java)) {
                        switchFragment(indexFriendMine)
                    } else {
                        //dong nothing
                    }
                }

                HomeConstant.RECORD -> {
                    toRecord()
                }

                else -> {

                }
            }
        }
    }


    /**
     * 登录
     */
    private fun gotoLogin() {
        RouteServiceManager.getInstance().accountProvider?.login(this, RESULT_LOGIN)
    }


    /**
     * 切换tab统一入口
     * @param tabFlag 目前支持的值有[HomeConstant.TO_CALENDAR],[HomeConstant.TO_ACCOMPANY]
     * [HomeConstant.TO_TOPIC],[HomeConstant.TO_DISCOVERY],[HomeConstant.TO_HOME_PAGE]
     * @param homeExtra tab切换初始化参数
     */
    override fun switchFragmentByHomeExtra(tabFlag: Int, homeExtra: HomeExtra?) {
        var isMiss = false
        when (tabFlag) {
            HomeConstant.TO_CALENDAR ->                 //日历
                if (isShowType(CalendarFragment::class.java)) {
                    switchFragment(index_Calendar)
                } else {
                    isMiss = true
                }

            HomeConstant.TO_ACCOMPANY ->                 //陪伴
                isMiss = true

            HomeConstant.TO_TOPIC ->                 //话题
                if (isShowType(NewTopicFragment::class.java)) {
                    switchTopicFragmentByHomeExtra(homeExtra)
                } else {
                    isMiss = true
                }

            HomeConstant.TO_DISCOVERY ->                 //发现
                if (isShowType(DiscoveryFragment::class.java)) {
                    switchFragment(index_Discovery)
                } else {
                    isMiss = true
                }

            HomeConstant.TO_BABYRECORD -> {
                isMiss = switchBabyRecordByExtra(homeExtra)
            }

            HomeConstant.TO_STAGE_MANAGER -> switchManagerFragmentByHomeExtra(homeExtra)
            HomeConstant.TO_FRIEND_MINE -> {
                if (isShowType(FriendIdentityMineFragment::class.java)) {
                    switchFragment(indexFriendMine)
                } else {
                    isMiss = true
                }
            }

            else -> isMiss = true
        }
        if (isMiss) {
            var dynamicHomeIndex = dynamicHomeIndex
            if (dynamicHomeIndex >= fragments.size) {
                //找不到首页，默认显示第一个tab
                dynamicHomeIndex = 0
            }
            switchFragment(dynamicHomeIndex)
        }
    }

    override fun onFeedChanged(feed: Int) {}

    override fun onNotifyChanged(notifyCount: Int) {}

    override fun onPmChanged(pmCount: Int) {}


    override fun onRequestChanged(requestCount: Int) {}

    override fun setCircleRefresh() {
        mTopicFragment?.onRefreshCurrentFragment()
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (mBabyRecordFragment != null && mBabyRecordFragment?.isVisible == true) {
            mBabyRecordFragment?.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun scrollToTopOnTab() {
        super.scrollToTopOnTab()
        if (mHomeFragment is IHomeFragment) {
            (mHomeFragment as IHomeFragment).scrollToTopForRefresh()
        }
    }

    override fun onNewIntentCalendar(tabIndex: Int, homeExtra: HomeExtra?) {
        switchFragmentByHomeExtra(tabIndex, homeExtra)
    }

}