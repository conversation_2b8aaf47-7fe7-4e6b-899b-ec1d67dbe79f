package cn.mama.pregnant.module.calendar.bean

import android.os.Parcelable
import cn.mama.exposure.bean.ReportEventBean
import cn.mama.pregnant.mamaloginshare.bean.MiniInfo
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class HealthyManageItem(
    /**
     * 图
     */
    val pic: String?,
    /**
     * 跳转类型
     */
    val type: String?,
    /**
     * 链接
     */
    val url: String?,
    /**
     * 小程序
     */
    val miniInfo: MiniInfo?,

    @SerializedName("report_event")
    val reportEvent: ReportEventBean?,
) : Parcelable
