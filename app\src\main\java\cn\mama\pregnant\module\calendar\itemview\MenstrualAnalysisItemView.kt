package cn.mama.pregnant.module.calendar.itemview

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.transition.Visibility
import cn.mama.pregnant.business.bean.calendar.MenstrualAnalysis
import cn.mama.pregnant.business.bean.calendar.MenstrualAnalysisItem
import cn.mama.pregnant.business.bean.calendar.MenstrualAnalysisLastStatusEnum
import cn.mama.pregnant.business.consts.ReportConstants.ItemType
import cn.mama.pregnant.business.util.ReportTools
import cn.mama.pregnant.business.util.monitor.ItemViewMonitorHelper
import cn.mama.pregnant.business.util.monitor.MonitorHelper
import cn.mama.pregnant.business.view.ItemViewBindingDelegate
import cn.mama.pregnant.databinding.CollectMenstrualPeriodBinding
import cn.mama.pregnant.databinding.ItemMenstrualAnalysisBinding
import cn.mama.pregnant.databinding.LayoutLastMenstruationChildBinding
import cn.mama.pregnant.databinding.LayoutMenstrualAnalysisChildBinding
import cn.mama.pregnant.module.calendar.adapter.PeriodChartDataAdapter
import cn.mama.pregnant.widget.view.chart.MoonBarChartView
import cn.mama.pregnant.widget.view.recycleview.base.ViewHolder
import cn.mama.pregnant.widget.view.recycleview.bean.RecyclerViewBean
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.StringUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.mama.babyrecord.mouble.pregnancyweight.widget.PeriodBarChartView
import util.HtmlUtil


/**
 * 14.12.0:经期分析 itemView
 *
 * <AUTHOR>
 * @date 2025/01/23
 */
class MenstrualAnalysisItemView(private val lifecycle: Lifecycle, private val clickAction: () -> Unit) :
    ItemViewBindingDelegate<RecyclerViewBean, ItemMenstrualAnalysisBinding>() {

    private val itemViewMonitorHelper: ItemViewMonitorHelper by lazy {
        ItemViewMonitorHelper(lifecycle)
    }

    override fun isForViewType(item: RecyclerViewBean, position: Int): Boolean {
        return item.type == CollectItemType.COLLECT_MENSTRUAL_ANALYSIS
    }

    override fun initData(
        holder: ViewHolder,
        item: RecyclerViewBean,
        position: Int,
        binding: ItemMenstrualAnalysisBinding,
    ) {
        val entity = item.data as? MenstrualAnalysis ?: return
        val context = holder.itemView.context

        val rvMenstrualAnalysis = binding.rvMenstrualAnalysis
        var adapter = rvMenstrualAnalysis.adapter as? MenstrualAnalysisChildAdapter
        val list = ArrayList<MenstrualAnalysisItem>()
        entity.duration?.let {
            it.title = "经期天数"
            it.range = "正常范围3-7天"
            it.lastWeekDesc="最近一次经期"
            it.isFirst=true
            list.add(it)
        }
        entity.cycle?.let {
            it.title = "月经周期"
            it.range = "正常范围21-35天"
            it.lastWeekDesc="最近一次月经周期"
            it.isFirst=false
            list.add(it)
        }
        if (adapter == null) {
            adapter = MenstrualAnalysisChildAdapter(lifecycle, list)
            rvMenstrualAnalysis.adapter = adapter
            rvMenstrualAnalysis.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

        } else {
            adapter.setNewInstance(list)
        }
        //可见才曝光
        MonitorHelper.registerView(binding.btnMoreMenstrualDate,ReportTools.createReportEventBean("MOON_RECORD_PERIOD_MORE",
            ItemType.BUTTON), lifecycle) {
            clickAction.invoke()
        }
    }

    override fun initListener(
        holder: ViewHolder,
        item: RecyclerViewBean,
        position: Int,
        binding: ItemMenstrualAnalysisBinding,
    ) {
        //do nothing
    }

    private class MenstrualAnalysisChildAdapter(
        val lifecycle: Lifecycle,
        bean: MutableList<MenstrualAnalysisItem>,
    ) : BaseQuickAdapter<MenstrualAnalysisItem, BaseViewHolder>(
        cn.mama.pregnant.R.layout.layout_menstrual_analysis_child,
        bean,
    ) {
        private val itemViewMonitorHelper: ItemViewMonitorHelper by lazy {
            ItemViewMonitorHelper(lifecycle)
        }

        @SuppressLint("SetTextI18n")
        override fun convert(holder: BaseViewHolder, item: MenstrualAnalysisItem) {
            val childBinding = LayoutMenstrualAnalysisChildBinding.bind(holder.itemView)
            childBinding.tvTitle.text = item.title
            childBinding.tvLastdateConetent.text = item.lastData.toString()
            childBinding.tvComparedateConetent.text = HtmlUtil.fromHtml(item.previous)
            childBinding.tvDesc.text = item.desc
            childBinding.tvMoonChartSafeRangeTips.text = item.range
            childBinding.tvLastdateTitle.text=item.lastWeekDesc

            var dataStatus=""
            var dataStatusColor=0
            if(item.lastDataStatus==MenstrualAnalysisLastStatusEnum.NORMAL.type){
                dataStatus="正常"
                dataStatusColor=ContextCompat.getColor(
                    context,cn.mama.pregnant.business.R.color.green121)
            }else if(item.lastDataStatus==MenstrualAnalysisLastStatusEnum.SHORT.type){
                dataStatus="偏短"
                dataStatusColor=ContextCompat.getColor(
                    context,cn.mama.pregnant.business.R.color.yellow104)
            }else{
                dataStatus="偏长"
                dataStatusColor=ContextCompat.getColor(
                    context,cn.mama.pregnant.business.R.color.yellow104)
            }
            childBinding.tvLastdateState.text=dataStatus
            childBinding.tvLastdateState.setBackgroundColor(dataStatusColor)
            if (item.isRule.isNullOrEmpty()) {
                childBinding.tvDescState.visibility= View.GONE
            }else{
                childBinding.tvDescState.visibility= View.VISIBLE
                val isRule=item.isRule=="1"
                childBinding.tvDescState.text=if(isRule)"规律" else "不规律"
                childBinding.tvDescState.setBackgroundColor( ContextCompat.getColor(
                    context,
                    if (isRule) cn.mama.pregnant.business.R.color.green121 else cn.mama.pregnant.business.R.color.yellow104
                ))
            }

            val periodChartDataAdapter = PeriodChartDataAdapter()
            if(item.isFirst){
                childBinding.cvMoonChart.isVisible=true
                childBinding.cvMoonPeriod.isVisible=false
                setupMoonBarChartView(
                    childBinding, periodChartDataAdapter.mapMoonBarChart(item.list)
                )
            }else{
                childBinding.cvMoonChart.isVisible=false
                childBinding.cvMoonPeriod.isVisible=true
                setupPeriodChartView(
                    childBinding, periodChartDataAdapter.mapPeriodBarChart(item.list)
                )
            }
        }

        override fun onViewRecycled(holder: BaseViewHolder) {
            super.onViewRecycled(holder)
            itemViewMonitorHelper.unregisterView(holder)
        }

        /**
         * 新的表格
         * */
        private fun setupMoonBarChartView(
            binding: LayoutMenstrualAnalysisChildBinding,
            dataList: List<MoonBarChartView.MoonData>?,
        ) {
            if (dataList.isNullOrEmpty()) {
                binding.clMoonChart.isVisible = false
            } else {
                binding.clMoonChart.isVisible = true
                binding.cvMoonChart.setNewData(dataList)


            }
        }


        /**
         * 旧的表格
         * */
        private fun setupPeriodChartView(
            binding: LayoutMenstrualAnalysisChildBinding,
            dataList: List<PeriodBarChartView.Data>?,
        ) {
            if (dataList.isNullOrEmpty()) {
                binding.clMoonChart.isVisible = false
            } else {
                binding.clMoonChart.isVisible = true
                binding.cvMoonPeriod.setNewData(dataList)


            }
        }

    }
}