package cn.mama.pregnant.module.calendar.bean

import android.os.Parcelable
import cn.mama.exposure.bean.ReportEventBean
import cn.mama.pregnant.business.bean.calendar.UltrasonicBean
import cn.mama.pregnant.business.bean.tools.CheckInCategoryItem
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * 日历工具
 */
@Parcelize
data class CalendarToolList(
    var list: List<CalendarToolItem>,
    //b超测排卵
    var ultrasonic: UltrasonicBean? = null,
    //孕前检查记录
    @SerializedName("pre_pregnancy")
    var prePregnancy: PrePregnancyItem? = null,
    /**
     * 健康管理
     */
    @SerializedName("healthy_manage_list")
    val healthyItemList: List<HealthyManageItem>? = null,
    /**
     * 日常清单
     */
    @SerializedName("daily_checklist")
    val dailyItemList: List<CheckInCategoryItem>? = null,
    //工具BI
    @SerializedName("tool_list_bi")
    val biList: List<ToolBIItem>? = null,
) : Parcelable


@Parcelize
data class CalendarToolItem(
    //menstrual=大姨妈开关，makelove=同房，mood=心情，ovulation=排卵试纸，temperature=体温，leucorrhea=白带，folic_acid=叶酸，ad=广告跳链
    var type: String? = null,
    var title: String? = null,
    var words: String? = null,//文字链
    var url: String? = null,//文字链跳转链接
    @SerializedName("after_record_words")
    var afterRecordWords: String? = null,
    @SerializedName("after_record_url")
    val afterRecordUrl: String? = null,
    @SerializedName("tool_icon_first")
    var iconFirst: String,
    @SerializedName("tool_icon_second")
    var iconSecond: String,
    @SerializedName("ad_status")
    var adStatus: Int,//广告参与状态：0-未参与，1-未打卡，2-已打卡
    @SerializedName("before_report_event")
    var beforeReportEvent: ReportEventBean? = null,
    @SerializedName("after_report_event")
    var afterReportEvent: ReportEventBean? = null,
) : Parcelable

@Parcelize
data class ToolBIItem(
    var type: String? = null,
    @SerializedName("add_report_event")
    var addReportEvent: ReportEventBean? = null,
    @SerializedName("title_report_event")
    var textReportEvent: ReportEventBean? = null,
) : Parcelable

@Parcelize
data class PrePregnancyItem(
    var count: Int = 0,
) : Parcelable