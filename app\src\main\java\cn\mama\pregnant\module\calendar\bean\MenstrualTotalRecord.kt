package cn.mama.pregnant.module.calendar.bean

import android.os.Parcelable
import cn.mama.exposure.bean.ReportEventBean
import cn.mama.pregnant.business.bean.calendar.MenstrualAnalysis
import cn.mama.pregnant.business.bean.calendar.PregMenstrualBean
import cn.mama.pregnant.business.bean.calendar.TemperatureServerBean
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * 记经期-汇总记录
 *
 * <AUTHOR>
 * @date 2021/9/2
 */
@Parcelize
class MenstrualTotalRecord : Parcelable {

    /**
     * 记经期
     */
    @SerializedName("pre_menstrual")
    var preMenstrual: PregMenstrualBean? = null

    /**
     * 体温
     */
    var temperature: TemperatureServerBean? = null

    /**
     * 排卵试纸
     */
    var ovulation: SummaryInfo? = null

    /**
     * 同房
     */
    @SerializedName("makelove")
    var makeLove: SummaryInfo? = null

    /**
     * 白带
     */
    var leucorrhea: SummaryInfo? = null

    /** 体重 */
    var weight: SummaryInfo? = null

    /** 补叶酸 */
    @SerializedName("folic_acid")
    var folicAcid: SummaryInfo? = null

    /** 最后一次月经 */
    @SerializedName("menstrual_delay_reason")
    val menstruationDelayReason: MenstruationDelayReason? = null

    /**
     * 记经期
     */
    @SerializedName("menstrual_analysis")
    var menstrualAnalysis: MenstrualAnalysis? = null

    /***
     * 记录汇总-整个页面
     */
    @SerializedName("report_event")
    var reportEvent: ReportEventBean? = null

    /**板块配置的顺序*/
    val configuration: MutableList<ConfigurationBean>? = null
}

@Parcelize
data class ConfigurationBean(
    /**板块名称*/
    val name: String? = null,
    /**板块类型*/
    val type: String? = null,
) : Parcelable

@Parcelize
data class SummaryInfo(
    /**
     * 最近记录
     */
    @SerializedName("new_record")
    var newRecord: String? = null,

    /**
     * 最近记录-状态
     */
    @SerializedName("new_record_status")
    var newRecordStatus: String? = null,

    /**
     * 是否有经期记录
     */
    @SerializedName("has_menstrual")
    var hasMenstrual: Int = 0,

    /**
     * 记录次数
     */
    var time: Long = 0,
    /**
     * 标题
     */
    var title: String? = null,
    /**
     * 副标题
     */
    var subtitle: String? = null,
    /**
     * 最新记录的标题
     */
    @SerializedName("new_record_title")
    var newRecordTitle: String? = null,
    /**
     * 次数的标题
     */
    @SerializedName("time_title")
    var timeTitle: String? = null,

    @SerializedName("report_event")
    var reportEvent: ReportEventBean? = null,

    @SerializedName("add_report_event")
    var addReportEvent: ReportEventBean? = null,

    ) : Parcelable

/**
 * 是否有经期记录, true-有
 */
fun SummaryInfo.hasMenstrual(): Boolean = hasMenstrual == 1

/**是否没有经期记录 */
fun SummaryInfo.isEmptyMenstrual(): Boolean = hasMenstrual == 0