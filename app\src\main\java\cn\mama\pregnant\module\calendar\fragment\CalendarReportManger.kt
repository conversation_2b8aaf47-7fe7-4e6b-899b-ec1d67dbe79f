package cn.mama.pregnant.module.calendar.fragment

import android.view.View
import androidx.lifecycle.Lifecycle
import cn.mama.pregnant.business.bean.ReportEventParams
import cn.mama.pregnant.business.bean.mapReportEventBean
import cn.mama.pregnant.business.consts.ReportConstants
import cn.mama.pregnant.business.consts.ReportConstants.ReportItemType
import cn.mama.pregnant.business.util.monitor.MonitorHelper
import cn.mama.pregnant.business.util.reportEvent

/**
 * 日历bi集合
 */
class CalendarReportManger(
    private val lifecycle: Lifecycle
) {

    /**
     * 开始备孕入口
     */
    val preparePregnancyEvent by reportEvent(
        itemType = ReportItemType.BUTTON,
        itemName = "",
        position = "MOON_BECOMEPREGNANCY_START"
    )

    /** 年视图 */
    val yearScheduleEvent by lazy {
        ReportEventParams(
            ReportConstants.Position.Moon.MOON_ANNUALVIEW,
            ReportItemType.BUTTON
        ).mapReportEventBean()
    }

    /** 记经期_大姨妈来了  记录结果   流量*/
    val symptomResultVolumeEvent by lazy {
        ReportEventParams(
            ReportConstants.Position.Moon.MOON_RECORD_SYMPTOM_RESULT,
            ReportItemType.BUTTON
        )
            .setItemMark { it.itemName = "流量" }
            .mapReportEventBean()
    }

    /** 记经期_大姨妈来了  记录结果  颜色*/
    val symptomResultColorEvent by lazy {
        ReportEventParams(
            ReportConstants.Position.Moon.MOON_RECORD_SYMPTOM_RESULT,
            ReportItemType.BUTTON
        )
            .setItemMark { it.itemName = "颜色" }
            .mapReportEventBean()
    }

    /** 记经期_大姨妈来了  记录结果  疼痛程度*/
    val symptomResultPainEvent by lazy {
        ReportEventParams(
            ReportConstants.Position.Moon.MOON_RECORD_SYMPTOM_RESULT,
            ReportItemType.BUTTON,
        )
            .setItemMark { it.itemName = "痛经" }
            .mapReportEventBean()
    }

    fun setupSymptomResultVisEvent(
        volumeView: View,
        colorView: View,
        painView: View,
        isVisible: Boolean
    ) {
        if (isVisible) {
            MonitorHelper.registerView(volumeView, symptomResultVolumeEvent, lifecycle)
            MonitorHelper.registerView(colorView, symptomResultColorEvent, lifecycle)
            MonitorHelper.registerView(painView, symptomResultPainEvent, lifecycle)
        } else {
            MonitorHelper.unregisterView(volumeView)
            MonitorHelper.unregisterView(colorView)
            MonitorHelper.unregisterView(painView)
            symptomResultVolumeEvent.isExposure = false
            symptomResultColorEvent.isExposure = false
            symptomResultPainEvent.isExposure = false
        }
    }

}