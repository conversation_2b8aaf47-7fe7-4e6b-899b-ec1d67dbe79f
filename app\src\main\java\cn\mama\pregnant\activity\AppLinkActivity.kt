package cn.mama.pregnant.activity

import android.os.Bundle
import cn.mama.exposure.util.ExposuerUtil
import cn.mama.pregnant.business.account.bean.UserInfo
import cn.mama.pregnant.business.consts.IGlobalRouteProviderConsts
import cn.mama.pregnant.business.util.AppUtil
import cn.mama.pregnant.business.util.CalendarHelper
import cn.mama.pregnant.module.splash.SplashActivity
import cn.mama.pregnant.module.splash.util.SplashHomeUtil
import cn.mama.pregnant.share.AppLinkShare
import com.alibaba.android.arouter.launcher.ARouter
import com.trello.rxlifecycle3.components.RxActivity

/**
 * 外部link跳转
 * Created by Administrator on 2016/4/7.
 */
class AppLinkActivity : RxActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (UserInfo.instance().ptMode == UserInfo.MODE_PREGNANTACTION && CalendarHelper.getMenstruationDays() <= 0) {
            ARouter.getInstance().build(IGlobalRouteProviderConsts.SPLASH_PATH).navigation()
            finish()
            return
        }
        if (AppUtil.isHomeActivityAlive()) {
            if (SplashHomeUtil.getInstance().isShowSplash) {
                SplashHomeUtil.getInstance().clearShowSplash()
                goToSplash(true)
                finish()
                return
            }
            if (intent?.data != null) {
                ExposuerUtil.getInstance()
                    .setUserId(UserInfo.instance().uid)
                ExposuerUtil.getInstance().requestUriTrack(intent.data)
            }
            intent.action = AppLinkShare.KEY_INTENT_ACTION_VIEW
            AppLinkShare.goToLink(this, intent)
            finish()
        } else {
            goToSplash(false)
        }
        if (AppLinkShare.isFinishRightNow(intent)) {
            finish()
        }
    }

    private fun goToSplash(splashInit: Boolean) {
        val intent = intent
        SplashActivity.startAppLinkLaunch(this, intent, splashInit)
    }


}