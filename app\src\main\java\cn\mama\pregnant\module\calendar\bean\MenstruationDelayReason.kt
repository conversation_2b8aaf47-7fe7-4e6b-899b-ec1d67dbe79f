package cn.mama.pregnant.module.calendar.bean

import android.os.Parcelable
import cn.mama.pregnant.business.bean.discovery.Discovery
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * 14.8.0:增加经期推迟原因
 */
@Parcelize
data class MenstruationDelayReason(
    /**
     * 推迟日期描述
     */
    @SerializedName("delay_date_desc")
    val delayReasonDesc: String? = null,

    @SerializedName("list")
    val itemList: List<Discovery>? = null,
) : Parcelable
