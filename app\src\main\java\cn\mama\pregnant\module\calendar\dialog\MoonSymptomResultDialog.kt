package cn.mama.pregnant.module.calendar.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.GridLayoutManager
import cn.mama.pregnant.business.bean.calendar.MenstrualDetail
import cn.mama.pregnant.business.bean.calendar.MenstrualOptions
import cn.mama.pregnant.business.bean.calendar.MenstrualStatus
import cn.mama.pregnant.business.util.monitor.ItemViewMonitorHelper
import cn.mama.pregnant.databinding.DialogMenstruationStatusSheetBinding
import cn.mama.pregnant.databinding.LayoutMenstruationStatusItemBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import image.helper.loadByNormal

/**
 * 今日经期记录SheetDialog
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
class MoonSymptomResultDialog(
    context: Context,
    /**
     * 经期状态选项
     */
    private val menstrualOptions: MenstrualOptions,
    /**
     * 已记录的经期状态
     */
    detail: MenstrualDetail?,
) : BottomSheetDialog(context) {

    private var menstrualDetail: MenstrualDetail? = detail?.copy()

    /**
     * 保存监听
     */
    var onSaveListener: ((MenstrualDetail?) -> Unit)? = null

    private val statusSheetBinding by lazy {
        DialogMenstruationStatusSheetBinding.inflate(LayoutInflater.from(context))
    }

    init {
        setContentView(statusSheetBinding.root)
        initView()
        initData()
        initListener()
    }

    private fun initView() {
        //解决圆角背景 原因：由于它上面蒙了一层布局 design_bottom_sheet是系统的布局，直接找到它，然后给它设全透明就好了
        val bottomSheet =
            delegate.findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.run {
            setBackgroundColor(
                ContextCompat.getColor(context, cn.mama.pregnant.business.R.color.transparent)
            )
            //内容全部展开
            val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
    }

    private fun initData() {
        with(statusSheetBinding.rvFlow) {
            adapter = StatusAdapter(
                lifecycle,
                menstrualOptions.volumeList.orEmpty().toMutableList(),
                menstrualDetail?.volume,
            ) {
                menstrualDetail = (menstrualDetail ?: MenstrualDetail()).copy(volume = it)
            }
            layoutManager = GridLayoutManager(context, 5)
        }
        with(statusSheetBinding.rvColor) {
            adapter = StatusAdapter(
                lifecycle,
                menstrualOptions.colorList.orEmpty().toMutableList(),
                menstrualDetail?.color,
            ) {
                menstrualDetail = (menstrualDetail ?: MenstrualDetail()).copy(color = it)
            }
            layoutManager = GridLayoutManager(context, 5)
        }
        with(statusSheetBinding.rvPain) {
            adapter = StatusAdapter(
                lifecycle,
                menstrualOptions.painLevelList.orEmpty().toMutableList(),
                menstrualDetail?.painLevel,
            ) {
                menstrualDetail = (menstrualDetail ?: MenstrualDetail()).copy(painLevel = it)
            }
            layoutManager = GridLayoutManager(context, 5)
        }
    }

    private fun initListener() {
        statusSheetBinding.ivCancel.setOnClickListener { dismiss() }

        statusSheetBinding.tvSave.setOnClickListener {
            dismiss()
            onSaveListener?.invoke(menstrualDetail ?: MenstrualDetail())
        }
    }

    private class StatusAdapter(
        private val lifecycle: Lifecycle,
        optionList: MutableList<MenstrualStatus>,
        private var selectedMenstrualStatus: MenstrualStatus? = null,
        private val onItemSelected: (MenstrualStatus?) -> Unit = {}
    ) : BaseQuickAdapter<MenstrualStatus, BaseViewHolder>(
        cn.mama.pregnant.R.layout.layout_menstruation_status_item,
        optionList,
    ) {
        private val itemViewMonitorHelper: ItemViewMonitorHelper by lazy {
            ItemViewMonitorHelper(lifecycle)
        }

        @SuppressLint("NotifyDataSetChanged")
        override fun convert(holder: BaseViewHolder, item: MenstrualStatus) {
            val childBinding = LayoutMenstruationStatusItemBinding.bind(holder.itemView)
            childBinding.ivIconStatus.loadByNormal(item.img)
            childBinding.tvStatusName.text = item.name
            childBinding.ivSelect.isVisible = selectedMenstrualStatus == item

            itemViewMonitorHelper.registerView(holder.itemView, item.reportEvent) {
                val selectedItem = if (selectedMenstrualStatus == item) null else item
                selectedMenstrualStatus = selectedItem
                onItemSelected.invoke(selectedItem)
                notifyDataSetChanged()
            }
        }

        override fun onViewRecycled(holder: BaseViewHolder) {
            super.onViewRecycled(holder)
            itemViewMonitorHelper.unregisterView(holder)
        }

    }

}