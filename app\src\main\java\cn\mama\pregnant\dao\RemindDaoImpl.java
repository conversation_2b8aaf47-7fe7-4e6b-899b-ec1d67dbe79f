package cn.mama.pregnant.dao;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import cn.mama.pregnant.business.dao.TaskManager;
import util.sp.SharePrencesUtils;

/**
 * 提醒数据接口的一种实现
 *
 * <AUTHOR>
 */
public class RemindDaoImpl implements RemindDao {

    private final SharedPreferences mEateTaskPref;

    RemindDaoImpl(Context context) {
        mEateTaskPref = context.getSharedPreferences(
            SharePrencesUtils.PREF_NAME_WEEK_TASK, Context.MODE_PRIVATE);
    }



    @Override
    public void clearWeekTask() {
        TaskManager.Companion.getInstance().getTaskDao().clearWeekTask();
    }

    @Override
    public void clearAll() {
        clearWeekTask();

    }

    @Override
    public void setNoEateTaskDone(String foodid) {
        String result = mEateTaskPref.getString("key_food_id", "");
        StringBuilder strB = new StringBuilder();
        strB.append(result);
        if (!"".equals(result)) {
            strB.append("#");
        }
        strB.append(foodid);
        mEateTaskPref.edit().putString("key_food_id", strB.toString()).apply();

    }

    @Override
    public boolean getNoEateTaskDone(String foodid) {
        boolean resultBo = false;
        String result = mEateTaskPref.getString("key_food_id", "");
        if ("".equals(result)) {
            return false;
        }
        String[] str = result.split("#");
        for (String s : str) {
            if (!TextUtils.isEmpty(foodid)) {
                if (foodid.equals(s)) {
                    resultBo = true;
                    break;
                }
            }
        }
        return resultBo;
    }




}
