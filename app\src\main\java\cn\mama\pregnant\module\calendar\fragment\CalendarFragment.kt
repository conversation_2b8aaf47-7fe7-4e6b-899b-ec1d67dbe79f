package cn.mama.pregnant.module.calendar.fragment

import android.animation.Animator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.view.View
import android.widget.CompoundButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.Lifecycle
import base.mvp.factory.CreatePresenter
import cn.mama.adsdk.utils.PvHelper
import cn.mama.exposure.bean.ReportEventBean
import cn.mama.pregnancy.user.baby.ChooseMenstrualActivity
import cn.mama.pregnancy.user.baby.NewChooseStageActivity
import cn.mama.pregnancy.user.baby.tool.AddBabyManager.Companion.goToChooseDateParent
import cn.mama.pregnancy.user.user.LoginActivity
import cn.mama.pregnant.R
import cn.mama.pregnant.business.account.bean.BaByInfo
import cn.mama.pregnant.business.account.bean.BaByInfo.OnNetworkSwitchBabyCallback
import cn.mama.pregnant.business.account.bean.UserInfo
import cn.mama.pregnant.business.base.BaseMvpFragment
import cn.mama.pregnant.business.bean.CommBannerBean.BannerList
import cn.mama.pregnant.business.bean.baby.ExtraStatusDataBean
import cn.mama.pregnant.business.bean.calendar.CalendarDataBean
import cn.mama.pregnant.business.bean.calendar.CalendarDataListBean
import cn.mama.pregnant.business.bean.calendar.CalendarInfo
import cn.mama.pregnant.business.bean.calendar.CalendarToolDataBean
import cn.mama.pregnant.business.bean.calendar.DayBean
import cn.mama.pregnant.business.bean.calendar.MakeLoveBean
import cn.mama.pregnant.business.bean.calendar.MenstrualDetail
import cn.mama.pregnant.business.bean.tools.CheckInCategoryItem
import cn.mama.pregnant.business.calendar.GatherContract
import cn.mama.pregnant.business.calendar.mmkv.CalendarMmkvUtil
import cn.mama.pregnant.business.consts.AppwidgetType
import cn.mama.pregnant.business.consts.CalendarAlertEnum
import cn.mama.pregnant.business.consts.Constants
import cn.mama.pregnant.business.consts.ExposureContract
import cn.mama.pregnant.business.consts.HomeConstant
import cn.mama.pregnant.business.consts.IntentHelpConsts
import cn.mama.pregnant.business.consts.MenstrualCycle
import cn.mama.pregnant.business.consts.ReportConstants.ItemType
import cn.mama.pregnant.business.consts.ReportConstants.ReportItemType
import cn.mama.pregnant.business.consts.ReportConstants.ReportPosition
import cn.mama.pregnant.business.event.BabyPointBlockEvent
import cn.mama.pregnant.business.event.CalendarChoosePregnantEvent
import cn.mama.pregnant.business.event.CategoryUpdateEvent
import cn.mama.pregnant.business.event.CheckInEvent
import cn.mama.pregnant.business.event.HideMenstrualAnalysisEvent
import cn.mama.pregnant.business.event.InspectionRefreshEvent
import cn.mama.pregnant.business.event.MenstrualChangedEvent
import cn.mama.pregnant.business.event.MenstrualModifyEvent
import cn.mama.pregnant.business.event.MenstrualPostDialogEvent
import cn.mama.pregnant.business.event.MyRedEvent
import cn.mama.pregnant.business.event.OvulationModifyEvent
import cn.mama.pregnant.business.provider.IBuinessToolsProvider
import cn.mama.pregnant.business.provider.IMainPublicProvider
import cn.mama.pregnant.business.router.RouteServiceManager
import cn.mama.pregnant.business.util.CalendarUtils
import cn.mama.pregnant.business.util.NewTimeFormatUtil
import cn.mama.pregnant.business.util.ReportTools
import cn.mama.pregnant.business.util.expand.bind
import cn.mama.pregnant.business.util.expand.setMultipleClickListener
import cn.mama.pregnant.business.util.expand.zeroDefault
import cn.mama.pregnant.business.util.monitor.MonitorHelper
import cn.mama.pregnant.business.util.monitor.MonitorHelper.registerViewLazy
import cn.mama.pregnant.business.util.reportClickEvent
import cn.mama.pregnant.business.util.reportCloseEventAsync
import cn.mama.pregnant.business.util.reportImpressionEvent
import cn.mama.pregnant.business.util.reportImpressionEventDelay
import cn.mama.pregnant.business.util.serializable
import cn.mama.pregnant.business.view.CommonDialogBuilder
import cn.mama.pregnant.business.view.DialogClick
import cn.mama.pregnant.business.view.switchbutton.MaSwitchButton
import cn.mama.pregnant.module.calendar.activity.CalendarActivity
import cn.mama.pregnant.module.calendar.activity.FolicAcidDetailsActivity
import cn.mama.pregnant.module.calendar.activity.LeucorrheaRecordActivity
import cn.mama.pregnant.module.calendar.activity.MakeLoveRecordActivity
import cn.mama.pregnant.module.calendar.activity.MenstrualReportActivity
import cn.mama.pregnant.module.calendar.bean.CalendarToolItem
import cn.mama.pregnant.module.calendar.bean.CalendarToolList
import cn.mama.pregnant.module.calendar.bean.HealthyManageItem
import cn.mama.pregnant.module.calendar.bean.TipsData
import cn.mama.pregnant.module.calendar.dialog.MoonSymptomResultDialog
import cn.mama.pregnant.module.calendar.listener.OnMonthCalendarChangedListener
import cn.mama.pregnant.module.calendar.presenter.CalendarContract
import cn.mama.pregnant.module.calendar.presenter.CalendarPresenter
import cn.mama.pregnant.module.calendar.utils.CalendarDialogUtils
import cn.mama.pregnant.module.calendar.utils.CalendarManager
import cn.mama.pregnant.module.calendar.view.CalendarHealthyCardView
import cn.mama.pregnant.module.calendar.view.MonthCalendar
import cn.mama.pregnant.module.checkin.view.widget.CalendarDailyCheckInView
import cn.mama.pregnant.module.dailyanalyze.activity.DailyAnalyzeActivity.Companion.launch
import cn.mama.pregnant.module.prepregnancy.model.TaskStateConstant
import de.greenrobot.event.EventBus
import image.GlideLoader
import imp.SimpleAnimatorListener
import io.reactivex.Observable
import org.joda.time.LocalDate
import util.ClickListenerProxy
import util.DisplayUtil
import util.DrawableUtil
import util.PublicMethod
import util.RoundAttrs
import util.RunUtil
import util.contact.TimeNumber
import util.dp
import util.setGone
import util.setRoundRectBg
import util.status.StatusBarUtil
import util.use
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 姨妈日历
 *
 * <AUTHOR>
 * @date 2018/5/8
 */
@CreatePresenter(CalendarPresenter::class)
class CalendarFragment : BaseMvpFragment<CalendarContract.View?, CalendarContract.Presenter?>(),
    CalendarContract.View, View.OnClickListener {
    private var isOpenForecast: Boolean = if(BaByInfo.instance().getMenstrual()==null) false else BaByInfo.instance().getMenstrual().isOpenForecast == 1
    private var dateTime: LocalDate? = null
    private var calendarInfo: CalendarInfo? = null
    private var calendarToolList: CalendarToolList? = null
    private val isTips = false//是否开启每日提示
    private var tipsData: TipsData? = null//每日提示
    private var isFromActivity = false//是否是calendarActivity下的fragment
    private var alertEnum: CalendarAlertEnum = CalendarAlertEnum.NONE
    private var hidden = false
    private var isFirstTime = true //第一次不显示备孕报告弹窗
    private val refreshToolList = AtomicBoolean(false) //从打卡wap退出后刷新工具
    private val refreshCategoryContent = AtomicBoolean(false)//更新打卡分类
    private var bannerAd: BannerList? = null//体温计广告
    private var currentStateTips = "今日处于"
    private var currentState = ""
    private var mMonthCalendar: MonthCalendar? = null
    private var scrollview: NestedScrollView? = null
    private var calendarTips: View? = null//姨妈提示
    private var ivMakeLoveIcon: ImageView? = null
    private var tvMakeLoveTips: TextView? = null
    private var mTvStageName: TextView? = null
    private var mTvStageValue: TextView? = null
    private var mTvSafetyPeriod: TextView? = null

    /**名词解释栏 */
    private var mLlInstructionDesc: LinearLayout? = null
    private var calendarArrow: ImageView? = null////提示箭头
    private var groupBlock: Group? = null
    private var mTvMainBlockTitle: TextView? = null
    private var mLlMainBlock: LinearLayout? = null

    /**同房 */
    private var mClInSexual: ConstraintLayout? = null
    private var mIvSexual: ImageView? = null
    private var mLlNoSwitch: LinearLayout? = null
    private var mTextSexual: TextView? = null//同房数据文案

    /**
     * B超测排卵
     */
    private var mClBUltrasonic: ConstraintLayout? = null
    private var mTextBUltrasonic: TextView? = null
    private lateinit var mAddBUltrasonic: ImageView
    private var tvBUltrasonicTips: TextView? = null

    /**
     * 记排卵日
     */
    private var mClOvulationDay: ConstraintLayout? = null
    private lateinit var mSwitchOvulationDay: MaSwitchButton

    /**体温 */
    private var mClTemperature: ConstraintLayout? = null
    private var mTextTemperature: TextView? = null//体温数据文案
    private lateinit var mImgAddTemperature: ImageView//体温无数据文案

    /**
     * 记体温入口
     */
    private var tvTemperatureTips: TextView? = null

    /**
     * 补叶酸
     */
    private var mClFolicAcid: ConstraintLayout? = null
    private lateinit var mSwitchFolicAcid: MaSwitchButton
    private var mIvFolicAcid: ImageView? = null

    /**排卵试纸点击 */
    private var mClOvulate: ConstraintLayout? = null
    private var mTextOvulate: TextView? = null//体温数据文案
    private lateinit var mImgAddOvulate: ImageView//体温无数据文案

    /**白带 */
    private var mClWhites: ConstraintLayout? = null
    private var mTextWhites: TextView? = null
    private lateinit var mImgAddWhites: ImageView
    private var mIvWhitesRecord: ImageView? = null

    /** 日记 */
    private lateinit var clDiary: ConstraintLayout
    private lateinit var ivDiaryIcon: ImageView

    /** 开始备孕 */
    private lateinit var clPreparePregnancy: ConstraintLayout
    private lateinit var ivPregnancyIcon: ImageView

    /**设置经期 */
    private var mClPeriodSet: ConstraintLayout? = null
    private var mTvPeriodSet: TextView? = null

    /**记录汇总 */
    private var mClRecordSummary: ConstraintLayout? = null
    private var mTvPregnant2: TextView? = null
    private var mBtnBack: ImageView? = null
    private var mBtnBarRight: ImageView? = null
    private var mTextBtnBack: TextView? = null
    private var mTextRight: TextView? = null
    private var mBarTitle: TextView? = null
    private var mInPeriod: TextView? = null
    private var mStatusBar: View? = null
    private var mNoLogin: TextView? = null
    private var mViewMensesLeft: View? = null
    private lateinit var switchInPeriod: MaSwitchButton

    /**经期症状记录*/
    private lateinit var groupMoonSymptomRecord: Group
    private lateinit var tvMoonVolumeValue: TextView
    private lateinit var tvMoonColorValue: TextView
    private lateinit var tvMoonPainLevelValue: TextView
    private lateinit var switchSexual: MaSwitchButton
    private var clReport: ConstraintLayout? = null//好孕报告
    private var clAd: ConstraintLayout? = null//体温计广告
    private var tvAd: TextView? = null
    private var imgAdClose: ImageView? = null
    private var tvAdTag: TextView? = null
    private var tvUseInstructions: TextView? = null

    /**
     * 健康管理
     */
    private lateinit var healthyCardView: CalendarHealthyCardView

    /**
     * 日常清单
     */
    private lateinit var dailyCheckInView: CalendarDailyCheckInView

    private var calendarDialogUtils: CalendarDialogUtils? = null////备孕工具弹窗
    private var hasMenstrual = 0 //默认是0
    private var calendarToolDataBean: CalendarToolDataBean? = null
    private var mOnSwitchBabyCallback: OnNetworkSwitchBabyCallback? = null

    private val toRecordSummaryEvent = ReportTools.createReportEventBean(
        ReportPosition.BP_TODAY_TTL_RECORD, ReportItemType.BUTTON
    )

    /** 体温上报 */
    private var tempTextReportEvent: ReportEventBean? = null
    private var tempAddReportEvent: ReportEventBean? = null

    /** 排卵试纸上报 */
    private var ovulationTextReportEvent: ReportEventBean? = null
    private var ovulationAddReportEvent: ReportEventBean? = null

    /** 大姨妈试纸上报 */
    private var menstrualTextReportEvent: ReportEventBean? = null
    private var menstrualAddReportEvent: ReportEventBean? = null

    /** 同房上报 */
    private var loveTextReportEvent: ReportEventBean? = null
    private var loveAddReportEvent: ReportEventBean? = null

    /** B超上报 */
    private var bOvulationTextReportEvent: ReportEventBean? = null
    private var bOvulationAddReportEvent: ReportEventBean? = null

    /** 补叶酸上报 */
    private var folateTextReportEvent: ReportEventBean? = null
    private var folateAddReportEvent: ReportEventBean? = null

    /** 白带上报 */
    private var whiteTextReportEvent: ReportEventBean? = null
    private var whiteAddReportEvent: ReportEventBean? = null

    /** 设为排卵日上报 */
    private var setupTextReportEvent: ReportEventBean? = null

    /** 日记上报 */
    private var diaryTextReportEvent: ReportEventBean? = null

    /**
     * 日历bi集合
     * */
    private val calendarReportManger by lazy {
        CalendarReportManger(viewLifecycleOwner.lifecycle)
    }

    /** 年视图 */
    private val yearScheduleLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            mMonthCalendar?.toToday()
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_calendar

    @SuppressLint("UseRequireInsteadOfGet")
    override fun init(savedInstanceState: Bundle?) {
        initStatusBarColor()

        mTextBtnBack = findViewById(cn.mama.pregnant.business.R.id.text_back) as TextView
        mBtnBack = findViewById(R.id.iv_back) as ImageView
        mBtnBack?.visibility = View.GONE
        mBarTitle = findViewById(R.id.title) as TextView
        mBtnBarRight = findViewById(cn.mama.pregnant.tools.R.id.iv_ok) as ImageView
        mBtnBarRight?.visibility = View.GONE
        mTextRight = findViewById(cn.mama.pregnancy.community.R.id.bar_right_text) as TextView
        mStatusBar = findViewById(R.id.statusBar)
        calendarTips = findViewById(R.id.calendar_tips)
        ivMakeLoveIcon = findViewById(R.id.iv_make_love_icon) as ImageView
        tvMakeLoveTips = findViewById(R.id.tv_make_love_tips) as TextView
        mTvStageName = findViewById(R.id.tv_stage_name) as TextView
        mTvStageValue = findViewById(R.id.tv_stage_value) as TextView
        mTvSafetyPeriod = findViewById(R.id.tv_safety_period) as TextView
        mLlInstructionDesc = findViewById(R.id.ll_instruction_desc) as LinearLayout
        calendarArrow = findViewById(R.id.calendar_arrow) as ImageView
        groupBlock = findViewById(R.id.group_block) as Group
        mTvMainBlockTitle = findViewById(R.id.tv_main_block_title) as TextView
        mLlMainBlock = findViewById(R.id.ll_main_block) as LinearLayout
        mLlNoSwitch = findViewById(R.id.ll_no_switch) as LinearLayout
        mClInSexual = findViewById(R.id.cl_in_sexual) as ConstraintLayout
        mIvSexual = findViewById(R.id.iv_sexual_record) as ImageView
        mClOvulate = findViewById(R.id.cl_in_ovulate) as ConstraintLayout
        mTextOvulate = findViewById(R.id.ovulate_text) as TextView
        mImgAddOvulate = findViewById(R.id.ovulate_add_img) as ImageView
        mTextSexual = findViewById(R.id.sexual_size_text_record) as TextView
        mClOvulationDay = findViewById(R.id.cl_ovulation_day) as ConstraintLayout
        mSwitchOvulationDay = findViewById(R.id.switch_ovulation_day) as MaSwitchButton
        mClTemperature = findViewById(R.id.cl_in_temperature) as ConstraintLayout
        mTextTemperature = findViewById(R.id.temperature_text) as TextView
        mImgAddTemperature = findViewById(R.id.temperature_add_img) as ImageView
        tvTemperatureTips = findViewById(R.id.tv_tool_tips) as TextView

        mClBUltrasonic = findViewById(R.id.cl_b_ultrasonic) as ConstraintLayout
        mAddBUltrasonic = findViewById(R.id.b_ultrasonic_add_img) as ImageView
        mTextBUltrasonic = findViewById(R.id.b_ultrasonic_text) as TextView
        tvBUltrasonicTips = findViewById(R.id.tv_b_ultrasonic_tips) as TextView

        mClFolicAcid = findViewById(R.id.cl_folic_acid) as ConstraintLayout
        mSwitchFolicAcid = findViewById(R.id.switch_folic_acid) as MaSwitchButton
        mIvFolicAcid = findViewById(R.id.iv_folic_record) as ImageView
        mClWhites = findViewById(R.id.cl_in_whites) as ConstraintLayout
        mTextWhites = findViewById(R.id.whites_text) as TextView
        mImgAddWhites = findViewById(R.id.whites_add_img) as ImageView
        mIvWhitesRecord = findViewById(R.id.iv_whites_record) as ImageView
        clPreparePregnancy = findViewById(R.id.cl_prepare_pregnancy) as ConstraintLayout
        ivPregnancyIcon = findViewById(R.id.iv_pregnancy_icon) as ImageView
        clDiary = findViewById(R.id.cl_diary) as ConstraintLayout
        ivDiaryIcon = findViewById(R.id.iv_diary_icon) as ImageView
        mClPeriodSet = findViewById(R.id.cl_set_period) as ConstraintLayout
        mTvPeriodSet = findViewById(R.id.tv_set_period) as TextView
        mClRecordSummary = findViewById(R.id.cl_record_summary) as ConstraintLayout
        mTvPregnant2 = findViewById(R.id.tv_pregnant2) as TextView
        mNoLogin = findViewById(R.id.tv_no_login) as TextView
        mViewMensesLeft = findViewById(R.id.view_menses_left)
        mInPeriod = findViewById(R.id.in_period) as TextView
        switchInPeriod = findViewById(R.id.switch_in_period) as MaSwitchButton
        groupMoonSymptomRecord = findViewById(R.id.group_moon_symptom_record) as Group
        tvMoonVolumeValue = findViewById(R.id.tv_moon_volume_value) as TextView
        tvMoonColorValue = findViewById(R.id.tv_moon_color_value) as TextView
        tvMoonPainLevelValue = findViewById(R.id.tv_moon_pain_level_value) as TextView
        switchSexual = findViewById(R.id.switch_sexual) as MaSwitchButton
        mMonthCalendar = findViewById(R.id.calendar_layout) as MonthCalendar
        clReport = findViewById(R.id.cl_pregnancy_report) as ConstraintLayout
        clAd = findViewById(R.id.cl_calendar_ad) as ConstraintLayout
        tvAd = findViewById(R.id.tv_calendar_ad) as TextView
        tvUseInstructions = findViewById(R.id.tv_use_instructions) as TextView
        imgAdClose = findViewById(R.id.iv_calendar_ad_close) as ImageView
        tvAdTag = findViewById(R.id.tv_tag) as TextView
        healthyCardView = findViewById(R.id.healthyCardView) as CalendarHealthyCardView
        dailyCheckInView = findViewById(R.id.dailyCheckInView) as CalendarDailyCheckInView
        dailyCheckInView.setup(
            viewLifecycleOwner.lifecycle,
            childFragmentManager,
            iLoadingDialog = this,
        )
        mStatusBar?.updateLayoutParams {
            height = StatusBarUtil.getStatusBarHeight(mContext)
        }
        if (UserInfo.instance().isBabyMode) {
            //育儿期，白带模块在同房后面
            mLlMainBlock?.let {
                it.removeView(mClWhites)
                it.addView(mClWhites, it.indexOfChild(mClInSexual) + 1)
            }
        }
        mStatusBar?.visibility = View.VISIBLE
        scrollview = findViewById(R.id.scrollview) as NestedScrollView
        isFromActivity = arguments?.getBoolean(ARGS_IS_FROM_ACTIVITY) ?: false
        alertEnum = arguments.serializable(ARGS_ALERT_ENUM) ?: CalendarAlertEnum.NONE
        setupTopBar(isFromActivity)
        setupStyleByStatus()
        setupRoundBg()
        initData()
        initListener()
    }

    private fun initData() {
        calendarDialogUtils = CalendarDialogUtils(mContext, viewLifecycleOwner.lifecycle)
        calendarDialogUtils?.preloadData()
        calendarToolDataBean = CalendarMmkvUtil.INSTANCE.getBean(
            CalendarMmkvUtil.CALENDAR_TOOL_DATA,
            CalendarToolDataBean::class.java
        )
        presenter?.fetchInitData()
        presenter?.setupSwitchModeBtn()
        reportPageBI()
    }

    private fun initListener() {
        mBarTitle?.setOnClickListener { launchYearSchedule() }
        clReport?.setOnClickListener(this)
        clAd?.setOnClickListener(this)
        imgAdClose?.setOnClickListener(this)
        listOf(
            findViewById(R.id.iv_menses_icon),
            findViewById(R.id.in_period),
            findViewById(R.id.iv_menses_record),
            findViewById(R.id.menses_hot_zone),
            findViewById(R.id.tv_no_login)
        )
            .setMultipleClickListener { gotoPeriodList() }
        mLlInstructionDesc?.setOnClickListener(this)
        tvUseInstructions?.setOnClickListener { onMenstrualRecordTipsClick() }
        mTextBtnBack?.setOnClickListener(this)
        mTextRight?.setOnClickListener {
            toRecordSummaryEvent.reportClickEvent()
            MenstrualReportActivity.launch(mContext)
        }

        //切换我怀孕了
        val btnPregnancyClickListener = View.OnClickListener {
            goToChooseDateParent()
        }
        mTvPregnant2?.setOnClickListener(btnPregnancyClickListener)
        //记录月经状态
        listOf(findViewById(R.id.tv_moon_volume_key), tvMoonVolumeValue).setMultipleClickListener {
            calendarReportManger.symptomResultVolumeEvent.reportClickEvent()
            showMoonSymptomResultDialog()
        }
        listOf(findViewById(R.id.tv_moon_color_key), tvMoonColorValue).setMultipleClickListener {
            calendarReportManger.symptomResultColorEvent.reportClickEvent()
            showMoonSymptomResultDialog()
        }
        listOf(
            findViewById(R.id.tv_moon_pain_level_key),
            tvMoonPainLevelValue
        ).setMultipleClickListener {
            calendarReportManger.symptomResultPainEvent.reportClickEvent()
            showMoonSymptomResultDialog()
        }
        //排卵试纸
        findViewById(R.id.ovulate_text).setOnClickListener {
            gotoOvulationTestStrip(
                isAdd = false,
                false
            )
        }
        mClOvulate?.setOnClickListener { gotoOvulationTestStrip(isAdd = false, true) }
        mImgAddOvulate?.setOnClickListener { gotoOvulationTestStrip(isAdd = true, true) }
        //记排卵日
        mClOvulationDay?.setOnClickListener(ClickListenerProxy.antiShake {
            setupTextReportEvent.reportClickEvent()
            val checked = !mSwitchOvulationDay.isChecked
            mSwitchOvulationDay.isChecked = checked
            presenter?.onOvulationDaySwitch(checked, calendarInfo)
        })
        mSwitchOvulationDay.setOnCheckedChangeListener { buttonView: CompoundButton, isChecked: Boolean ->
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            presenter?.onOvulationDaySwitch(isChecked, calendarInfo)
        }
        val ovulationSwitchEventBean = ReportTools.createReportEventBean(
            ReportPosition.MOON_OVULATION_SWITCH, ItemType.BUTTON
        )
        registerViewLazy(
            mClOvulationDay!!, ovulationSwitchEventBean,
            viewLifecycleOwner.lifecycle, null
        )
        //体温
        mClTemperature?.setOnClickListener(
            ClickListenerProxy.antiShake { showTemperatureDialog() })
        //B超测排卵
        findViewById(R.id.b_ultrasonic_text)?.setOnClickListener {
            ultrasonicClick(
                isIndex = true,
                false
            )
        }
        mClBUltrasonic?.setOnClickListener { ultrasonicClick(isIndex = true, true) }
        mAddBUltrasonic.setOnClickListener { ultrasonicClick(isIndex = false, true) }
        //补叶酸
        listOf(
            findViewById(R.id.iv_folic_acid), findViewById(R.id.tv_folic_acid),
            findViewById(R.id.iv_b_ultrasonic_record), findViewById(R.id.acid_hot_zone)
        ).setMultipleClickListener {
            folateTextReportEvent.reportClickEvent()
            FolicAcidDetailsActivity.launch(mContext)
        }
        mSwitchFolicAcid.setOnCheckedChangeListener { buttonView: CompoundButton, isChecked: Boolean ->
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            folateAddReportEvent.reportClickEvent()
            presenter?.onFolicAcidSwitch(isChecked, calendarInfo, calendarDialogUtils)
        }
        //白带
        findViewById(R.id.whites_text).setOnClickListener { showWhitesDialog() }
        findViewById(R.id.whites_add_img).setOnClickListener {
            whiteAddReportEvent.reportClickEvent()
            showWhitesDialog()
        }
        listOf(
            findViewById(R.id.iv_whites_icon), findViewById(R.id.in_whites),
            findViewById(R.id.iv_whites_record), findViewById(R.id.white_hot_zone)
        ).setMultipleClickListener {
            whiteTextReportEvent.reportClickEvent()
            LeucorrheaRecordActivity.launch(mContext)
        }
        // 开始备孕
        MonitorHelper.registerView(clPreparePregnancy, calendarReportManger.preparePregnancyEvent) {
            gotoChooseStagePage()
        }
        // 日记
        clDiary.setOnClickListener {
            diaryTextReportEvent.reportClickEvent()
            getBusinessToolProvider()?.launchDiaryList(mContext)
        }
        //同房
        listOf(
            findViewById(R.id.iv_sexual_icon), findViewById(R.id.in_sexual),
            findViewById(R.id.iv_sexual_record), findViewById(R.id.sexual_hot_zone)
        ).setMultipleClickListener {
            loveTextReportEvent.reportClickEvent()
            MakeLoveRecordActivity.launch(mContext)
        }
        mTextSexual?.setOnClickListener {
            presenter?.addSexualCount(calendarInfo, calendarDialogUtils)
        }
        //经期设置
        mClPeriodSet?.setOnClickListener { gotoChooseMenstrual() }
        //记录汇总跳转
        mClRecordSummary?.visibility = View.GONE
        //体温
        listOf(
            findViewById(R.id.iv_temperature_icon), findViewById(R.id.in_temperature),
            findViewById(R.id.iv_temperature_record), findViewById(R.id.temperature_hot_zone)
        ).setMultipleClickListener { gotoTemperature() }

        mMonthCalendar?.setOnMonthCalendarChangedListener(OnMonthCalendarChangedListener { info ->
            val selectedDate = info.date ?: return@OnMonthCalendarChangedListener
            if (selectedDate != dateTime) {
                presenter?.getCalendarListIfCacheInvalid(dateTime)
            }
            //这个回调第一次进来会执行两次,标记BI只上报其中一次,不相同才上报
            val isInSameDate = selectedDate == dateTime
            dateTime = selectedDate
            calendarInfo = info
            CalendarUtils.setMenstrualDate(info)
            //修改标题栏样式
            setupTopBarByDate(dateTime!!)
            //记录板块可见行设置
            setupRecordBlock(info, dateTime!!)
            if (UserInfo.instance().isLogin) {
                bindRecordBlockData(info)
            }

            showPregnancyRate(info)
        })
        switchInPeriod.setOnCheckedChangeListener { buttonView: CompoundButton, isChecked: Boolean ->
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            menstrualAddReportEvent.reportClickEvent()
            handleSwitchPeriod(isChecked, true)
        }
        switchSexual.setOnCheckedChangeListener { buttonView: CompoundButton, isChecked: Boolean ->
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            loveAddReportEvent.reportClickEvent()
            presenter?.onSexualSwitch(isChecked, calendarInfo, calendarDialogUtils)
        }
    }

    /**
     * b超测排卵点击事件
     * @param isIndex 是否是排卵首页
     * */
    private fun ultrasonicClick(isIndex: Boolean, isReport: Boolean) {
        if (isReport) {
            (if (isIndex) bOvulationTextReportEvent else bOvulationAddReportEvent).reportClickEvent()
        }
        if (!RouteServiceManager.getInstance().accountProvider.login(mContext)) {
            return
        }
        if (!CalendarManager.allowToast(mContext)) {
            return
        }
        getMainPublicProvider()?.ADUrlPaseCheck(
            mContext,
            null,
            if (isIndex)
                calendarToolList?.ultrasonic?.indexUrl
            else
                calendarToolList?.ultrasonic?.recordUrl,
            true
        )
    }

    private fun setupTopBar(isFromActivity: Boolean) {
        val layoutParams = mTextBtnBack!!.layoutParams as RelativeLayout.LayoutParams
        layoutParams.width = RelativeLayout.LayoutParams.WRAP_CONTENT
        layoutParams.height = RelativeLayout.LayoutParams.WRAP_CONTENT
        if (isFromActivity) {
            mBtnBack?.visibility = View.VISIBLE
            mBtnBack?.setOnClickListener(this)
            layoutParams.addRule(RelativeLayout.RIGHT_OF, mBtnBack!!.id)
        } else {
            layoutParams.leftMargin = PublicMethod.dip2px(mContext, 16f)
        }
        mTextBtnBack?.layoutParams = layoutParams
        val paddingVertical = PublicMethod.dip2px(mContext, 4f)
        val paddingHorizontal = PublicMethod.dip2px(mContext, 10f)
        mTextBtnBack?.setPadding(
            paddingHorizontal, paddingVertical, paddingHorizontal,
            paddingVertical
        )
        mTextBtnBack?.visibility = View.VISIBLE
        mTextBtnBack?.textSize = 14f
        val roundAttrs = RoundAttrs.Builder(mContext)
            .setCornerRadius(40f)
            .setBgColorRes(cn.mama.pregnant.business.R.color.CT14)
            .build()
        mTextBtnBack?.setRoundRectBg(roundAttrs)
        mTextBtnBack?.setTextColor(
            ContextCompat.getColor(
                mContext,
                cn.mama.pregnant.business.R.color.CT1
            )
        )
        switchBackBtnToReturnToday()
        mTextRight?.text = "记录汇总"
        toRecordSummaryEvent.reportImpressionEvent(false)
        mTextRight?.textSize = 15f
        mTextRight?.visibility = if (HomeConstant.isChargeService) View.GONE else View.VISIBLE
    }

    private fun showMoonSymptomResultDialog() {
        val options = calendarToolDataBean?.menstrualOptions ?: return
        val menstrualDetail = calendarInfo?.calendarDataBean?.menstrualDetail
        val dialog = MoonSymptomResultDialog(mContext, options, menstrualDetail)
        dialog.onSaveListener = { presenter!!.saveMenstruationStatus(calendarInfo!!, it) }
        dialog.show()
    }

    private fun showPregnancyRate(info: CalendarInfo) {
        val rage = info.rage
        //育儿和备孕阶段才显示好孕率
        val existData = !TextUtils.isEmpty(rage) && info.stage != MenstrualCycle.NODATA
        val isBaby = UserInfo.instance().isBabyMode
        val isPregnancy = UserInfo.instance().isPregnanActiontMode
        if ((isPregnancy || isBaby) && existData) {
            currentState = info.stageName
            mTvStageName?.text = String.format("%s%s", currentStateTips, currentState)
            mTvStageValue?.text = String.format("好孕率%s%%", rage)
            mTvStageName?.isVisible = true
            mTvStageValue?.isVisible = isPregnancy
        } else {
            mTvStageName?.isVisible = false
            mTvStageValue?.isVisible = false
        }
    }

    @SuppressLint("SetTextI18n")
    private fun setupStyleByStatus() {
        val userInfo = UserInfo.instance()
        val login = userInfo.isLogin
        val babyMode = userInfo.isBabyMode
        val pregnantMode = userInfo.isPregnantMode
        val pregnantActionMode = userInfo.isPregnanActiontMode
        mNoLogin?.visibility = if (login) View.GONE else View.VISIBLE
        //非备孕阶段才显示“安全期”
        mTvSafetyPeriod?.setText(if (babyMode) cn.mama.pregnant.business.R.string.baby_mode_safety_period else cn.mama.pregnant.business.R.string.pregnancy_mode_safety_period)
        ivMakeLoveIcon?.visibility = if (pregnantActionMode) View.VISIBLE else View.GONE
        tvMakeLoveTips?.visibility = if (pregnantActionMode) View.VISIBLE else View.GONE
        mClFolicAcid?.visibility = if (babyMode) View.GONE else View.VISIBLE
        clPreparePregnancy?.visibility =
            if (babyMode && !userInfo.hasSexMode()) View.VISIBLE else View.GONE

        val menstrual = BaByInfo.instance().getMenstrual()

        mTvPeriodSet?.text = if(isOpenForecast) "智能预测" else "当前周期" + (menstrual?.menstrual_cycle ?: "28") + "天"

        ReportTools.reportImpressionEvent(ReportItemType.MOON_YESUAN, ItemType.BUTTON)
        val title = when {
            babyMode -> "经期记录"
            pregnantMode -> "记录"
            else -> "备孕记录"
        }
        mTvMainBlockTitle?.text = title

        adjustBlockItemVisibility()
    }

    private fun adjustBlockItemVisibility() {
        val user = UserInfo.instance()
        when {
            user.isBabyMode -> {
                // 育儿阶段不显示“B超排卵”
                mClOvulate?.isVisible = true
                mClTemperature?.isVisible = true
                mClBUltrasonic?.isVisible = false
            }

            user.isPregnantMode -> {
                // 怀孕阶段不显示“大姨妈”、“排卵试纸”、“记体温”、“B超排卵”
                findViewById(R.id.cl_in_period).isVisible = false
                mClOvulate?.isVisible = false
                mClTemperature?.isVisible = false
                mClBUltrasonic?.isVisible = false
            }
        }
    }

    private fun setupRoundBg() {
        setupCalendarTipsRound(12f)
        val blockRoundAttrs = RoundAttrs.Builder(mContext)
            .setBgColorRes(cn.mama.pregnant.common.R.color.white)
            .setCornerRadius(12F)
            .build()
        blockRoundAttrs.use(mLlMainBlock!!)
    }

    private fun setupCalendarTipsRound(radius: Float) {
        val calendarTipsRoundAttrs = RoundAttrs.Builder(mContext)
            .setBgColorRes(cn.mama.pregnant.common.R.color.white)
            .setRoundRectBg(0f, 0f, radius, radius)
            .build()
        calendarTips?.setRoundRectBg(calendarTipsRoundAttrs)
    }

    private fun setupTopBarByDate(calendarDate: LocalDate) {
        val currDate = calendarDate.year.toString() + "年" + calendarDate.monthOfYear + "月"
        mBarTitle?.text = currDate
        ContextCompat.getDrawable(
            mContext, cn.mama.pregnant.business.R.drawable.common_icon_beiyun_jilu_date2
        )?.let {
            it.setBounds(0, 0, 16F.dp(), 17F.dp())
            mBarTitle?.setCompoundDrawables(null, null, it, null)
        }
        mBarTitle?.compoundDrawablePadding = 2F.dp()
        val isCurrentDay = LocalDate() == calendarDate
        if (isFromActivity) {
            mTextBtnBack?.visibility = if (isCurrentDay) View.GONE else View.VISIBLE
        } else if (isCurrentDay) {
            //日历选中的日期是今日
            //显示我怀孕了,每次都曝光，后面可能改
            if (UserInfo.instance().isPregnanActiontMode) {
                ReportTools.reportImpressionEvent(
                    ReportPosition.CALENDAR_BECOME_PREGNANT, ReportItemType.PT_CALENDAR,
                    "我怀孕了"
                )
                switchBackBtnToPregnant()
            } else {
                mTextBtnBack?.visibility = View.GONE
            }
        } else {
            switchBackBtnToReturnToday()
        }
    }

    private fun switchBackBtnToReturnToday() {
        mTextBtnBack?.setCompoundDrawables(null, null, null, null)
        mTextBtnBack?.visibility = View.VISIBLE
        mTextBtnBack?.text = "回到今天"
    }

    private fun switchBackBtnToPregnant() {
        val drawable = DrawableUtil.getBoundsIcon(requireActivity(), R.drawable.ic_huaiyun_xin)
        mTextBtnBack?.setCompoundDrawables(null, null, drawable, null)
        mTextBtnBack?.compoundDrawablePadding = DisplayUtil.dip2px(mContext, 2f)
        mTextBtnBack?.visibility = View.VISIBLE
        mTextBtnBack?.text = "我怀孕了"
    }

    private fun setupRecordBlock(info: CalendarInfo, calendarDate: LocalDate) {
        val userInfo = UserInfo.instance()
        if (NewTimeFormatUtil.isDayBeforeToday(calendarDate.toString()) || userInfo.isNotLogin) {
            //今日之前或者还没登录就显示记录操作项
            groupBlock?.visibility = View.VISIBLE
            mLlNoSwitch?.visibility = View.GONE
        } else {
            groupBlock?.visibility = View.GONE
            mLlNoSwitch?.visibility = View.VISIBLE
        }
        val isMenstrual = info.stage == MenstrualCycle.MENSTRUAL
        mClWhites?.visibility = if (isMenstrual) View.GONE else View.VISIBLE
        groupMoonSymptomRecord.isVisible = isMenstrual
        calendarReportManger.setupSymptomResultVisEvent(
            tvMoonVolumeValue,
            tvMoonColorValue,
            tvMoonPainLevelValue,
            isMenstrual
        )
        showMenstruationStatus(info.calendarDataBean?.menstrualDetail)
        setupOvulationDayVisibility(isMenstrual)
    }

    override fun onVisibleChanged(firstVisible: Boolean) {
        super.onVisibleChanged(firstVisible)
        presenter?.getCalendarListIfCacheInvalid(dateTime)
        //刷新打卡入口
        if (refreshToolList.compareAndSet(true, false)) {
            presenter?.getCalendarToolList()
        }
        if (refreshCategoryContent.compareAndSet(true, false)) {
            presenter?.updateCheckInCategory()
        }
        //检查是否需要显示经期日历小组件引导
        RouteServiceManager.getInstance().buinessToolsProvider
            ?.showAppwidgetGuideDialogIfNeed(mContext, AppwidgetType.Moon)
        //显示好孕报告
        if (!isFirstTime) {
            showPregnancyReport()
        }
        isFirstTime = false
    }

    override fun onInvisibleChanged() {
        super.onInvisibleChanged()
        clReport?.clearAnimation()
    }

    @SuppressLint("UseRequireInsteadOfGet")
    override fun onClick(v: View) {
        val intent: Intent
        when (v.id) {
            cn.mama.pregnant.business.R.id.text_back -> if ("我怀孕了" == mTextBtnBack!!.text.toString()) {
                ReportTools.reportClickEvent(
                    ReportPosition.CALENDAR_BECOME_PREGNANT, ReportItemType.PT_CALENDAR,
                    "我怀孕了"
                )
                goToChooseDateParent()
            } else {
                mMonthCalendar?.toToday()
            }

            R.id.iv_back -> activity?.finish()
            cn.mama.pregnant.tools.R.id.iv_ok -> {
                intent = Intent(mContext, ChooseMenstrualActivity::class.java)
                intent.putExtra(ChooseMenstrualActivity.KEY_CALENDAR, true)
                startActivity(intent)
            }

            R.id.ll_instruction_desc -> if (tipsData != null) {
                if (isTips) {
                    //跳转
                    val jumpIntent = Intent()
                    jumpIntent.putExtra(
                        IntentHelpConsts.INTENT_TYPE,
                        tipsData!!.openType.toString()
                    )
                        .putExtra(IntentHelpConsts.INTENT_FROM, 0)
                    getMainPublicProvider()?.intentHelper(context, jumpIntent)

                } else {
                    if (!TextUtils.isEmpty(tipsData!!.introLink)) {
                        getMainPublicProvider()?.goToCommonWebActivity(
                            context,
                            tipsData?.introLink,
                            null
                        )
                    }
                }
            }

            R.id.cl_pregnancy_report -> navigationToAnalyze()
            R.id.cl_calendar_ad ->                 //跳转体温计广告
                if (bannerAd != null) {
                    val lukeInfo = bannerAd?.lukeInfo
                    if (lukeInfo != null) {
                        //LUKE广告
                        PvHelper.pvAds(mContext, lukeInfo.click_code)
                        getMainPublicProvider()?.ADUrlPaseCheck(
                            mContext,
                            lukeInfo.mAdControlBean(),
                            null,
                            true
                        )
                    } else {
                        //papi广告
                        ReportTools.reportClickEvent(bannerAd?.reportEventBean)
                        getMainPublicProvider()?.ADUrlPaseCheck(mContext, null, bannerAd?.url, true)
                    }
                }

            R.id.iv_calendar_ad_close -> {
                //关闭体温计广告
                bannerAd?.reportEventBean.reportCloseEventAsync()
                bannerAd?.lukeInfo?.run {
                    PvHelper.pvAds(mContext, close_code)
                }
                hideAd()
            }

            else -> {
            }
        }
    }

    private fun launchYearSchedule() {
        calendarReportManger.yearScheduleEvent.reportClickEvent()
        if (RouteServiceManager.getInstance().accountProvider?.login(this) == true) {
            RouteServiceManager.getInstance().buinessCoreProvider
                ?.createMenstrualYearScheduleIntent(mContext)
                ?.let { yearScheduleLauncher.launch(it) }
        }
    }

    private fun onMenstrualRecordTipsClick() {
        val tipBean =
            (if (calendarToolDataBean == null) null else calendarToolDataBean!!.menstrualTipBean)
                ?: return
        val reports = calendarToolDataBean?.report
        val toolEvent = reports?.menstrual_tips
        val eventBean = toolEvent?.reportEvent
        ReportTools.reportClickEvent(eventBean)
        getMainPublicProvider()?.goToCommonWebActivity(context, tipBean.url, null)
    }

    fun refreshDates() {
        mMonthCalendar?.refreshDates()
        mMonthCalendar?.toToday()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == Constants.REQUEST_PREGNANT) {
                presenter!!.onAddBabyActivityResult()
                if (mMonthCalendar != null && dateTime != null) {
                    mMonthCalendar?.refreshDates()
                }
            }
        }
    }

    override fun onCalendarList(list: CalendarDataListBean) {
        hasMenstrual = list.hasMenstrual
        if (mMonthCalendar != null && dateTime != null) {
            mMonthCalendar?.refreshDates()
            mMonthCalendar?.notifyMothCalendarChanged()
            presenter?.getCalendarToolList()
            //显示好孕报告
            showPregnancyReport()
        }
    }


    private fun handleSwitchPeriod(isChecked: Boolean, checkDate: Boolean) {
        if (calendarInfo == null || presenter == null) {
            return
        }
        // 大姨妈状态变化通知首页记经期卡片刷新
        //EventBus.getDefault().post(HomePeriodCardRefreshEvent())
        // 通知刷新积木的相册模块
        BabyPointBlockEvent(isDelay = true).post()

        //上报
        if (isChecked) {
            calendarDialogUtils?.calendarBiClick(GatherContract.MENSTRUALTOOL, "")
        } else {
            calendarDialogUtils?.calendarBiClick(GatherContract.MENSTRUALTOOL, null)
        }
        //本地弹窗优先级最高
        val periodText = mInPeriod!!.text.toString()
        if (presenter!!.checkMenstruationChangeEvent(
                calendarInfo!!,
                isChecked,
                periodText,
                checkDate
            )
        ) {
            //大姨妈修改校验不通过
            return
        }
        //接口弹窗优先级次之，本地弹窗弹出关闭不会继续请求帖子弹窗，防止弹窗连续弹出
        //大姨妈来了 打开开关
        if (checkDate && isChecked && periodText.contentEquals(mContext.getString(cn.mama.pregnant.business.R.string.menstrual_start))) {
            //获取帖子弹窗
            EventBus.getDefault().post(
                MenstrualPostDialogEvent(MenstrualPostDialogEvent.POST_SOURCE_TYPE_PERIOD)
            )
        }

        presenter!!.onMenstruationSwitch(calendarInfo!!, isChecked, periodText, checkDate)
    }

    private fun reportPageBI() {
        ReportTools.reportImpressionEvent(
            ReportPosition.CALENDAR_MOON_ENTER,
            ReportItemType.PT_BECOME_PREGNANT, "大姨妈统计点击"
        )
        ReportTools.reportImpressionEvent(
            ExposureContract.CALENDAR_SETTING_ENTRAN,
            ExposureContract.PT_CALENDAR
        )
        calendarReportManger.yearScheduleEvent.reportImpressionEventDelay()
    }

    /**
     * 显示好孕分析更新弹窗
     */
    @SuppressLint("ObjectAnimatorBinding")
    private fun showPregnancyReport() {
        if (isFromActivity) {
            //二级页面不弹窗
            return
        }
        val tips =
            CalendarMmkvUtil.INSTANCE.getValueTimeOut(CalendarMmkvUtil.KEY_HOME_MENSTRUAL_ANALYSIS)
        //如果存在通知并且今天未显示过动画
        if (!TextUtils.isEmpty(tips) && !HomeConstant.hasShowPregnancyReport) {
            HomeConstant.hasShowPregnancyReport = true
            clReport?.visibility = View.VISIBLE
            val animator = ObjectAnimator.ofFloat(clReport, "translationY", 1000f, 0f)
            animator.duration = 300
            animator.start()
            animator.addListener(object : SimpleAnimatorListener() {
                @SuppressLint("CheckResult")
                override fun onAnimationEnd(animation: Animator) {
                    Observable.timer(2500, TimeUnit.MILLISECONDS)
                        .bind(viewLifecycleOwner)
                        .subscribe { clReport?.visibility = View.GONE }
                }

                override fun onAnimationCancel(animation: Animator) {
                    clReport?.visibility = View.GONE
                }

            })
        }
    }

    private fun showWhitesDialog() {
        val calendarData = calendarInfo?.calendarDataBean
        val leucorrhea = calendarData?.leucorrhea
        calendarDialogUtils?.calendarBiClick(GatherContract.WHITES, leucorrhea)
        if (UserInfo.instance().isNotLogin) {
            gotoLogin()
            return
        }
        if (!CalendarManager.allowToast(mContext)) {
            return
        }
        calendarDialogUtils?.initWhitesDialog(
            true, leucorrhea, dateTime.toString(), 0,
            if (presenter != null) presenter!!.whitesDialogAd else null
        )
    }

    private fun showTemperatureDialog() {
        val calendarData = calendarInfo!!.calendarDataBean
        val temperature = calendarData?.temperature
        calendarDialogUtils?.calendarBiClick(GatherContract.TEMPERATURE, temperature)
        if (UserInfo.instance().isNotLogin) {
            gotoLogin()
            return
        }
        if (!CalendarManager.allowToast(mContext)) {
            return
        }
        if (CalendarManager.isTemperatureEmpty(temperature)) {
            tempAddReportEvent?.reportClickEvent()
            calendarDialogUtils?.checkBlueToothDetail { result: Boolean ->
                val dateText =
                    dateTime!!.monthOfYear.toString() + "月" + dateTime!!.dayOfMonth + "日"
                if (result) {
                    calendarDialogUtils?.initBindTemperatureDialog(dateText) {
                        calendarDialogUtils?.initTemperatureDialog(
                            true,
                            temperature,
                            dateTime.toString(),
                            dateText,
                            0,
                            false,
                            true,
                            true
                        )
                    }
                } else {
                    calendarDialogUtils?.initTemperatureDialog(
                        true,
                        temperature,
                        dateTime.toString(),
                        dateText,
                        0,
                        false,
                        false,
                        true
                    )
                }
            }
        } else {
            RouteServiceManager.getInstance().adultThermometerProvide?.goTemperatureListActivity(
                mContext
            )
        }
    }

    private fun goToChooseDateParent() {
        if (!CalendarManager.relevanceAllowToast()) {
            return
        }
        //前往预产期设置
        goToChooseDateParent(this, mContext)
    }

    private fun gotoChooseMenstrual() {
        if (!CalendarManager.allowToast(mContext)) {
            return
        }
        ReportTools.reportClickEvent(
            ExposureContract.CALENDAR_SETTING_ENTRAN, ExposureContract.PT_CALENDAR
        )
        val menstrualMode = UserInfo.instance().isPregnanActiontMode
        //跳转到 月经周期、月经天数 填写页（无上次月经）
        val intent = Intent(mContext, ChooseMenstrualActivity::class.java)
        intent.putExtra(ChooseMenstrualActivity.KEY_CALENDAR, true)
        //不切换备孕,但刷备孕首页
        intent.putExtra(ExtraStatusDataBean.KEY_UPDATE_MENSTRUAL, !menstrualMode)
        intent.putExtra(ExtraStatusDataBean.KEY_FROM_PERIODLIST, true)
        intent.putExtra(ExtraStatusDataBean.KEY_TRANSFORM, !menstrualMode)
        intent.putExtra(ExtraStatusDataBean.KEY_EDIT, true)
        startActivity(intent)
    }

    private fun gotoTemperature() {
        tempTextReportEvent.reportClickEvent()
        RouteServiceManager.getInstance().adultThermometerProvide?.goTemperatureActivity(mContext)
    }

    private fun gotoOvulationTestStrip(isAdd: Boolean, isReport: Boolean) {
        if (isReport) {
            (if (isAdd) ovulationAddReportEvent else ovulationTextReportEvent).reportClickEvent()
        }
        val calendarData = calendarInfo?.calendarDataBean
        val ovulation = calendarData?.ovulation
        calendarDialogUtils?.calendarBiClick(GatherContract.OVUATION, ovulation)
        if (!CalendarManager.allowToast(mContext)) {
            return
        }
        RouteServiceManager.getInstance().ovulationProvider?.gotoOvulationTestStrip(mContext)
    }

    private fun gotoPeriodList() {
        menstrualTextReportEvent.reportClickEvent()
        //跳转到月经工具
        calendarDialogUtils?.launchPeriodList(hasMenstrual, false)
    }

    /**
     * 跳转到好孕分析
     */
    private fun navigationToAnalyze() {
        clReport?.visibility = View.GONE
        CalendarMmkvUtil.INSTANCE.setValue(CalendarMmkvUtil.KEY_HOME_MENSTRUAL_ANALYSIS, "")
        EventBus.getDefault().post(HideMenstrualAnalysisEvent())
        //查看好孕分析报告
        launch(mContext, dateTime.toString(), false)
    }

    override fun onSwitchMenstrual(
        beanList: List<DayBean>,
        isChecked: Boolean,
        hasMenstrual: Int,
        isMenstrualCome: Boolean,
    ) {
        this.hasMenstrual = hasMenstrual
        if (mMonthCalendar != null && dateTime != null) {
            if (calendarInfo != null) {
                CalendarUtils.setMenstrualDate(calendarInfo)
            }
            mMonthCalendar?.refreshDates()
            mMonthCalendar?.notifyMothCalendarChanged()
            MenstrualModifyEvent().post() //刷新备孕头部
        }

        //月经期不显示白带
        mClWhites?.visibility = if (isChecked) View.GONE else View.VISIBLE
        if (isChecked) {
            mClOvulationDay?.visibility = View.GONE
        } else {
            setupOvulationDayVisibility(false)
        }

        //大姨妈来了开关,更新配置
        calendarToolList?.let { updateToolListConfig(it) }
    }

    override fun setTipsData(tipsData: TipsData) { //提示文案返回
        this.tipsData = tipsData
        setTips()
    }

    override fun setPeriodSwitchBtnClickable(clickable: Boolean) {
        switchInPeriod.isClickable = clickable
    }

    override fun setSexualSwitchBtnClickable(clickable: Boolean) {
        mClInSexual?.isClickable = clickable
        switchSexual.isClickable = clickable
    }

    override fun showSwitchPregnantBtn(show: Boolean) {
        mTvPregnant2?.visibility = if (show) View.VISIBLE else View.GONE
    }

    override fun showAd(bannerList: BannerList) {
        this.bannerAd = bannerList
        ReportTools.reportImpressionEvent(bannerList.reportEventBean)
        val lukeInfo = bannerList.lukeInfo
        if (lukeInfo != null) {
            PvHelper.pvAds(mContext, lukeInfo.pv_code)
            if (lukeInfo.content != null && !TextUtils.isEmpty(lukeInfo.content.title)) {
                //title表示品牌方
                currentStateTips = lukeInfo.content.title
                mTvStageName?.text = String.format("%s%s", currentStateTips, currentState)
            }
            if (TextUtils.isEmpty(lukeInfo.luke_tag)) {
                tvAdTag?.visibility = View.GONE
            } else {
                tvAdTag?.visibility = View.VISIBLE
                tvAdTag?.text = lukeInfo.luke_tag
            }
        }
        tvAd?.text = bannerList.name
        clAd?.visibility = View.VISIBLE
        setupCalendarTipsRound(0f)
    }

    override fun hideAd() {
        clAd?.visibility = View.GONE
        setupCalendarTipsRound(12f)
    }

    override fun onCalendarToolData() {
        calendarDialogUtils?.updateCalendarToolDataBean()
        calendarDialogUtils?.reportCalendarAll() //初始化曝光
        calendarToolDataBean = CalendarMmkvUtil.INSTANCE.getBean(
            CalendarMmkvUtil.CALENDAR_TOOL_DATA,
            CalendarToolDataBean::class.java
        )
        if (calendarToolDataBean?.report?.menstrual_tips != null) {
            ReportTools.reportImpressionEvent(calendarToolDataBean?.report?.menstrual_tips?.reportEvent)
        }
    }

    override fun complete() {
        switchInPeriod.isClickable = true
        switchSexual.isClickable = true
    }

    //设置mInPeriod的文案显示
    @SuppressLint("SetTextI18n")
    override fun bindRecordBlockData(info: CalendarInfo) {
        mInPeriod?.text = CalendarUtils.getPeriodNameByDate(mContext, info)
        //设置大姨妈开关状态
        if (info.switchOn == 1 || info.switchOn == 2) {
            if (!switchInPeriod.isChecked) {
                setPeriodChecked(true)
            }
        } else {
            if (switchInPeriod.isChecked) {
                setPeriodChecked(false)
            }
        }

        info.calendarDataBean?.run {
            if (makelove != null) { //当天是否有同房记录
                mTextSexual?.visibility = View.VISIBLE
                switchSexual.visibility = View.GONE
                setSexualSwitchBtnChecked(false)
                val spannableString = SpannableStringBuilder()
                spannableString.append(makelove.times)
                var timeDes = makelove.timeDes
                if (TextUtils.isEmpty(timeDes)) {
                    timeDes = "未记录时间"
                }
                timeDes?.let {
                    spannableString.append("\n").append(timeDes)
                    val absoluteSizeSpan = AbsoluteSizeSpan(30)
                    val colorSpan = ForegroundColorSpan(
                        ContextCompat.getColor(
                            mContext,
                            cn.mama.pregnant.business.R.color.base_thread_content
                        )
                    )
                    spannableString.setSpan(
                        absoluteSizeSpan,
                        spannableString.length - timeDes.length,
                        spannableString.length,
                        Spannable.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                    spannableString.setSpan(
                        colorSpan,
                        spannableString.length - timeDes.length,
                        spannableString.length,
                        Spannable.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                }
                mTextSexual?.text = spannableString
            } else {
                mTextSexual?.visibility = View.GONE
                switchSexual.visibility = View.VISIBLE
                switchSexual.isClickable = true
                setSexualSwitchBtnChecked(false)
            }
            if (temperature != null) { //当天是否有体温记录
                mTextTemperature?.visibility = View.VISIBLE
                mImgAddTemperature.visibility = View.GONE
                mTextTemperature?.text = (temperature.degree.zeroDefault()) + "℃"
            } else {
                mTextTemperature?.visibility = View.GONE
                mImgAddTemperature.visibility = View.VISIBLE
            }
            //当天是否有B超测试记录
            if (ultrasonic != null) {
                mTextBUltrasonic?.visibility = View.VISIBLE
                mAddBUltrasonic.visibility = View.GONE
                mTextBUltrasonic?.text = ultrasonic?.level
            } else {
                mTextBUltrasonic?.visibility = View.GONE
                mAddBUltrasonic.visibility = View.VISIBLE
            }
            if (ovulation != null) { //当天是否有排卵记录
                mTextOvulate?.visibility = View.VISIBLE
                mImgAddOvulate.visibility = View.GONE
                mTextOvulate?.text = ovulation?.ovulationDes.orEmpty()
            } else {
                mTextOvulate?.visibility = View.GONE
                mImgAddOvulate.visibility = View.VISIBLE
            }
            if (leucorrhea != null) { //当天是否有白带记录
                mTextWhites?.visibility = View.VISIBLE
                mImgAddWhites.visibility = View.GONE
                mTextWhites?.text = leucorrhea.leucorrheaStage
            } else {
                mTextWhites?.visibility = View.GONE
                mImgAddWhites.visibility = View.VISIBLE
            }
            val folicAcid = folicAcid
            mSwitchFolicAcid.isChecked = folicAcid != null && folicAcid.isCheck()
            val ovulationSwitch = ovulationSwitch
            mSwitchOvulationDay.isChecked = ovulationSwitch != null && ovulationSwitch.isCheck()

            //更新配置
            calendarToolList?.let { updateToolListConfig(it) }
        }
    }

    override fun updateToolListConfig(calendarToolList: CalendarToolList) {
        this.calendarToolList = calendarToolList

        // 设置记录点击事件
        for (item in calendarToolList.list) {
            when (item.type.orEmpty()) {
                GatherContract.MENSTRUALTOOL -> {
                    toolItemIcon(
                        item, R.id.iv_menses_icon, R.drawable.ic_menses,
                        R.id.iv_menses_record
                    )
                    toolItemTipsClick(
                        item,
                        R.id.tv_use_instructions,
                        switchInPeriod.isChecked,
                        R.id.menses_hot_zone
                    )
                }

                GatherContract.MAKELOVE -> {
                    toolItemIcon(
                        item, R.id.iv_sexual_icon, R.drawable.ic_calendar_love,
                        R.id.iv_sexual_record
                    )
                    toolItemTipsClick(
                        item,
                        R.id.tv_sexual_tips,
                        switchSexual.isGone,
                        R.id.sexual_hot_zone
                    )
                }

                GatherContract.OVUATION -> {
                    toolItemIcon(
                        item, R.id.iv_ovulate_icon, R.drawable.ic_calendar_ovulation,
                        R.id.iv_ovulate_record
                    )
                    toolItemTipsClick(
                        item,
                        R.id.tv_ovulate_tips,
                        mImgAddOvulate.isGone
                    )
                }

                GatherContract.TEMPERATURE -> {
                    toolItemIcon(
                        item, R.id.iv_temperature_icon, R.drawable.ic_temperature,
                        R.id.iv_temperature_record
                    )
                    toolItemTipsClick(
                        item,
                        R.id.tv_tool_tips,
                        mImgAddTemperature.isGone,
                        R.id.temperature_hot_zone
                    )
                }

                GatherContract.B_ULTRASONIC -> {
                    toolItemIcon(
                        item, R.id.iv_b_ultrasonic_icon, R.drawable.ic_calendar_b_ultrasonic,
                        R.id.iv_b_ultrasonic_record
                    )
                    toolItemTipsClick(
                        item,
                        R.id.tv_b_ultrasonic_tips,
                        mAddBUltrasonic.isGone
                    )
                }

                GatherContract.FOLATE -> {
                    toolItemIcon(
                        item, R.id.iv_folic_acid, R.drawable.ic_calendar_folic_acid,
                        R.id.iv_folic_record
                    )
                    toolItemTipsClick(
                        item,
                        R.id.tv_tool_folic,
                        mSwitchFolicAcid.isChecked,
                        R.id.acid_hot_zone
                    )
                }

                GatherContract.WHITES -> {
                    toolItemIcon(
                        item, R.id.iv_whites_icon, R.drawable.ic_leukorrhea,
                        R.id.iv_whites_record
                    )
                    toolItemTipsClick(
                        item,
                        R.id.tv_tool_whites,
                        mImgAddWhites.isGone,
                        R.id.white_hot_zone
                    )
                }

                GatherContract.DIARY -> {
                    toolItemIcon(item, ivDiaryIcon.id, R.drawable.ic_readypr_note_red, -1)
                }

                // 开始备孕
                GatherContract.PREPARE_PREGNANCY -> {
                    toolItemIcon(item, ivPregnancyIcon.id, R.drawable.ic_readypr_note_red, -1)
                }

                GatherContract.OVULATION_DAY -> {
                    toolItemIcon(
                        item, R.id.iv_ovulation_day, R.drawable.ic_ovulationday,
                        R.id.iv_switch_pregnancy_record
                    )
                    toolItemTipsClick(
                        item,
                        R.id.tv_tool_switch_pregnancy,
                        mSwitchOvulationDay.isChecked,
                        R.id.ovulation_day_hot_zone
                    )
                }

                else -> {
                    //DO NOTHING
                }
            }
        }

        if (calendarToolList.biList.isNullOrEmpty()) {
            return
        }
        // 统一处理日历上报事件（14.5.0版本）
        for (item in calendarToolList.biList) {
            when (item.type.orEmpty()) {
                GatherContract.MENSTRUALTOOL -> {
                    if (menstrualTextReportEvent == null) {
                        menstrualTextReportEvent = addCalendarReportEvent(
                            GatherContract.MENSTRUALTOOL,
                            findViewById(R.id.in_period),
                            true
                        )
                    }
                    if (menstrualAddReportEvent == null) {
                        menstrualAddReportEvent = addCalendarReportEvent(
                            GatherContract.MENSTRUALTOOL,
                            switchInPeriod,
                            false
                        )
                    }
                }

                GatherContract.MAKE_LOVE -> {
                    if (loveTextReportEvent == null) {
                        loveTextReportEvent = addCalendarReportEvent(
                            GatherContract.MAKE_LOVE,
                            findViewById(R.id.in_sexual),
                            true
                        )
                    }
                    if (loveAddReportEvent == null) {
                        loveAddReportEvent =
                            addCalendarReportEvent(GatherContract.MAKE_LOVE, switchSexual, false)
                    }
                }

                GatherContract.OVUATION -> {
                    if (ovulationTextReportEvent == null) {
                        ovulationTextReportEvent = addCalendarReportEvent(
                            GatherContract.OVUATION,
                            findViewById(R.id.in_ovulate),
                            true
                        )
                    }
                    if (ovulationAddReportEvent == null) {
                        ovulationAddReportEvent =
                            addCalendarReportEvent(GatherContract.OVUATION, mImgAddOvulate, false)
                    }
                }

                GatherContract.TEMPERATURE -> {
                    if (tempTextReportEvent == null) {
                        tempTextReportEvent = addCalendarReportEvent(
                            GatherContract.TEMPERATURE,
                            findViewById(R.id.in_temperature),
                            true
                        )
                    }
                    if (tempAddReportEvent == null) {
                        tempAddReportEvent = addCalendarReportEvent(
                            GatherContract.TEMPERATURE,
                            mImgAddTemperature,
                            false
                        )
                    }
                }

                GatherContract.B_OVULATION -> {
                    if (bOvulationTextReportEvent == null) {
                        bOvulationTextReportEvent = addCalendarReportEvent(
                            GatherContract.B_OVULATION,
                            findViewById(R.id.in_b_ultrasonic),
                            true
                        )
                    }
                    if (bOvulationAddReportEvent == null) {
                        bOvulationAddReportEvent = addCalendarReportEvent(
                            GatherContract.B_OVULATION,
                            mAddBUltrasonic,
                            false
                        )
                    }
                }

                GatherContract.FOLATE -> {
                    if (folateTextReportEvent == null) {
                        folateTextReportEvent = addCalendarReportEvent(
                            GatherContract.FOLATE,
                            findViewById(R.id.tv_folic_acid),
                            true
                        )
                    }
                    if (folateAddReportEvent == null) {
                        folateAddReportEvent =
                            addCalendarReportEvent(GatherContract.FOLATE, mSwitchFolicAcid, false)
                    }
                }

                GatherContract.WHITES -> {
                    if (whiteTextReportEvent == null) {
                        whiteTextReportEvent = addCalendarReportEvent(
                            GatherContract.WHITES,
                            findViewById(R.id.in_whites),
                            true
                        )
                    }
                    if (whiteAddReportEvent == null) {
                        whiteAddReportEvent =
                            addCalendarReportEvent(GatherContract.WHITES, mImgAddWhites, false)
                    }
                }

                GatherContract.SETUP_OVULATION_DAY -> {
                    if (setupTextReportEvent == null) {
                        setupTextReportEvent = addCalendarReportEvent(
                            GatherContract.SETUP_OVULATION_DAY,
                            findViewById(R.id.tv_ovulation_day),
                            true
                        )
                    }
                }

                GatherContract.DIARY -> {
                    if (diaryTextReportEvent == null) {
                        diaryTextReportEvent = addCalendarReportEvent(
                            GatherContract.DIARY,
                            findViewById(R.id.tv_diary_title),
                            true
                        )
                    }
                }
            }
        }
    }


    /**
     * 添加日历列表各事件上报（14.5.0统一点位）
     * @param type 列表类型
     * @param view 曝光组件
     * @param isTitle 是否是textReport
     * */
    private fun addCalendarReportEvent(
        type: String,
        view: View,
        isTitle: Boolean,
    ): ReportEventBean? {
        return this.calendarToolList?.biList?.find { it.type == type }
            ?.let { if (isTitle) it.textReportEvent else it.addReportEvent }?.also {
                MonitorHelper.registerView(
                    view,
                    it,
                    viewLifecycleOwner.lifecycle
                )
            }
    }


    fun setAlertEnum(alertEnum: CalendarAlertEnum) {
        this.alertEnum = alertEnum
    }

    override fun showAlertIfNeed() {
        // 数据加载完成才弹窗
        calendarToolList ?: return
        when (alertEnum) {
            CalendarAlertEnum.LEUKORRHEA -> showWhitesDialog()
            else -> Unit
        }
        alertEnum = CalendarAlertEnum.NONE
    }

    override fun showHealthyCard(healthyItemList: List<HealthyManageItem>) {
        healthyCardView.setNewData(viewLifecycleOwner.lifecycle, healthyItemList)
    }

    override fun showDailyCheckInCard(dailyItemList: List<CheckInCategoryItem>) {
        dailyCheckInView.setNewData(dailyItemList)
    }

    override fun showMenstruationStatus(detail: MenstrualDetail?) {
        tvMoonVolumeValue.text = detail?.volume?.name.orEmpty()
        tvMoonColorValue.text = detail?.color?.name.orEmpty()
        tvMoonPainLevelValue.text = detail?.painLevel?.name.orEmpty()
    }

    /**
     * 跳转孕前检查记录
     */
    private fun gotoChooseStagePage() {
        val identity = if (UserInfo.instance().isMaMa) 1 else 2
        NewChooseStageActivity.launch(activity, identity, false, UserInfo.MODE_PREGNANTACTION)
    }

    /**
     * 图标配置
     */
    private fun toolItemIcon(
        item: CalendarToolItem,
        firstId: Int,
        nativeIvFirst: Int,
        secondId: Int,
    ) {
        val ivMensIcon = findViewById(firstId) as? ImageView
        GlideLoader.with(mContext, ivMensIcon)
            .load(item.iconFirst, nativeIvFirst)
        if (secondId < 0) return
        val ivSecond = findViewById(secondId) as? ImageView ?: return
        if (TextUtils.isEmpty(item.iconSecond)) {
            //远程没有配置图标并且本地也没有默认图标
            ivSecond.visibility = View.GONE
        } else {
            ivSecond.visibility = View.VISIBLE
            GlideLoader.with(mContext, ivSecond).load(item.iconSecond)
        }
    }

    /**
     * 链接点击跳转
     * [hasRecord] 是否进行过记录或者打开开关
     */
    private fun toolItemTipsClick(
        item: CalendarToolItem?,
        viewId: Int,
        hasRecord: Boolean,
        hotZoneId: Int = 0,
    ) {
        item ?: return
        val tv = findViewById(viewId) as TextView
        if (hasRecord) {
            tv.text =
                if (TextUtils.isEmpty(item.afterRecordWords)) {
                    ""
                } else {
                    MonitorHelper.registerView(
                        tv,
                        item.afterReportEvent,
                        viewLifecycleOwner.lifecycle
                    )
                    appendUnderLine(item.afterRecordWords)
                }
            tv.setOnClickListener {
                item.afterReportEvent.reportClickEvent()
                getMainPublicProvider()?.ADUrlPaseCheck(mContext, null, item.afterRecordUrl, true)
            }

        } else {
            tv.text = if (TextUtils.isEmpty(item.words)) {
                ""
            } else {
                MonitorHelper.registerView(
                    tv,
                    item.beforeReportEvent,
                    viewLifecycleOwner.lifecycle
                )
                appendUnderLine(item.words)
            }
            tv.setOnClickListener {
                item.beforeReportEvent.reportClickEvent()
                getMainPublicProvider()?.ADUrlPaseCheck(mContext, null, item.url, true)
            }
        }
        if (hotZoneId == 0) {
            return
        }
        findViewById(hotZoneId).setGone(!tv.text.isNullOrBlank())
    }

    private fun getMainPublicProvider(): IMainPublicProvider? {
        return RouteServiceManager.getInstance().mainPublicProvider
    }

    private fun getBusinessToolProvider(): IBuinessToolsProvider? {
        return RouteServiceManager.getInstance().buinessToolsProvider
    }

    //拦截弹窗提示
    override fun showBanPoP(string: String) {
        showTipsDialog(string)
        setPeriodChecked(false)
    }

    //调整月经日期弹窗提示
    override fun showAdjustPop() {
        val content =
            "是否调整月经开始日期至" + dateTime!!.monthOfYear + "月" + dateTime!!.dayOfMonth + "日"
        CommonDialogBuilder()
            .setContent(content)
            .setListener(object : DialogClick {
                override fun onSureClick(dialog: AlertDialog) {
                    setPeriodChecked(true)
                    handleSwitchPeriod(isChecked = true, checkDate = false)
                    dialog.dismiss()
                }

                override fun onCancelClick(dialog: AlertDialog) {
                    dialog.dismiss()
                }
            })
            .build(mContext, cn.mama.pregnant.business.R.layout.dialog_common_no_title)
            .show()
    }

    override fun showTipsDialog(content: String) {
        CommonDialogBuilder()
            .setPositiveText(getString(cn.mama.pregnant.business.R.string.ok))
            .setCancelable(true)
            .setContent(content)
            .setListener(object : DialogClick {
                override fun onSureClick(dialog: AlertDialog) {
                    dialog.dismiss()
                }

                override fun onCancelClick(dialog: AlertDialog) {
                    dialog.dismiss()
                }
            })
            .build(mContext, cn.mama.pregnant.business.R.layout.dialog_common_single_button)
            .show()
    }

    override fun showAddSexualDialog(makeLove: MakeLoveBean) {
        val stage = if (calendarInfo == null) 0 else calendarInfo!!.stage
        calendarDialogUtils?.initSexualDialog(mTextSexual, stage, makeLove, dateTime.toString(), 0)
    }

    private fun setTips() {
        calendarArrow?.visibility = View.VISIBLE
        mLlInstructionDesc?.visibility = View.VISIBLE
    }

    //登录拦截
    override fun loginIntercept() {
        CommonDialogBuilder()
            .setContent("登录/注册后使用大姨妈记录功能，数据永久不丢失")
            .setPositiveText("登录后记录")
            .setPassiveText(getString(cn.mama.pregnant.business.R.string.cancel))
            .setListener(object : DialogClick {
                override fun onSureClick(dialog: AlertDialog) {
                    RouteServiceManager.getInstance()
                        .accountProvider
                        ?.login(activity, LOGIN)
                    dialog.dismiss()
                }

                override fun onCancelClick(dialog: AlertDialog) {
                    dialog.dismiss()
                }
            })
            .build(mContext, cn.mama.pregnant.business.R.layout.dialog_common_no_title)
            .show()
    }

    override fun setPeriodChecked(checked: Boolean) {
        switchInPeriod.isChecked = checked
    }

    override fun setSexualSwitchBtnChecked(checked: Boolean) {
        switchSexual.isChecked = checked
    }

    override fun setFolicAcidSwitchChecked(checked: Boolean) {
        mSwitchFolicAcid.isChecked = checked
    }

    override fun setOvulationSwitchChecked(checked: Boolean) {
        mSwitchOvulationDay.isChecked = checked
    }

    override fun gotoLogin() {
        LoginActivity.invoke(mContext)
    }

    override fun refreshMonthCalendar() {
        mMonthCalendar?.refreshDates()
    }

    private fun appendUnderLine(desc: String?): SpannableString {
        val recordTipsSpan = SpannableString(desc.orEmpty())
        recordTipsSpan.setSpan(UnderlineSpan(), 0, recordTipsSpan.length, 0)
        return recordTipsSpan
    }


    private fun setupOvulationDayVisibility(isMenstrual: Boolean) {
        val userInfo = UserInfo.instance()
        //设排卵日开关显示规则：1.月经期间不显示设排卵日开关, 2.备孕显示该开关，孕期、育儿不显示
        val showOvulationDay = !isMenstrual && userInfo.isPregnanActiontMode
        mClOvulationDay?.visibility = if (showOvulationDay) View.VISIBLE else View.GONE
    }

    override fun enableEventBus(): Boolean = true

    override fun canEventBusSticky(): Boolean = true

    /**
     * 备孕工具数据保存成功后返回
     */
    fun onEventMainThread(event: CalendarDataBean?) {
        if (event == null) {
            return
        }
        calendarDialogUtils?.dismiss()
        presenter?.onCalendarDataEvent(event, calendarInfo)
    }

    /**
     * 刷新分类内容
     */
    fun onEventMainThread(event: CategoryUpdateEvent) {
        refreshCategoryContent.set(true)
    }

    /**
     * 日常清单-打卡事件
     */
    fun onEventMainThread(event: CheckInEvent) {
        if (viewLifecycleOwner.lifecycle.currentState == Lifecycle.State.RESUMED) {
            //不处理当前页的打卡，只处理来自周汇总的打卡事件
        } else {
            refreshCategoryContent.set(true)
        }
    }

    fun onEventMainThread(event: OvulationModifyEvent?) {
        presenter?.onCalendarDataEvent(null, calendarInfo)
    }

    @SuppressLint("UseRequireInsteadOfGet")
    fun onEventMainThread(event: CalendarChoosePregnantEvent?) {
        if (activity != null && activity is CalendarActivity) {
            activity?.finish()
        }
    }


    @SuppressLint("SetTextI18n")
    fun onEventMainThread(event: MenstrualChangedEvent) {
        if (mMonthCalendar != null) {
            mMonthCalendar?.refreshDates()
            mMonthCalendar?.notifyMothCalendarChanged()
            presenter?.getCalendarToolList()
        }
        RunUtil.runOnUiThread {
            val menstrual = BaByInfo.instance().getMenstrual()
            isOpenForecast=menstrual.isOpenForecast==1
            mTvPeriodSet?.text = if(isOpenForecast) "智能预测" else "当前周期" + (menstrual?.menstrual_cycle ?: "28") + "天"
        }
    }


    /**
     * 备孕分析冒泡通知
     *
     * @param event
     */
    fun onEventMainThread(event: MyRedEvent?) {
        if (event != null && !hidden) {
            //日历可见
            if (event.type == MyRedEvent.MENSTRUAL_ANALYSIS) {
                HomeConstant.hasShowPregnancyReport = false
                CalendarMmkvUtil.INSTANCE.setValueTimeOut(
                    CalendarMmkvUtil.KEY_HOME_MENSTRUAL_ANALYSIS,
                    event.msg,
                    TimeNumber.TIME_DAY
                )
                showPregnancyReport()
            }
        }
    }

    /**
     * 备孕帖子弹窗
     */
    fun onEventMainThread(event: MenstrualPostDialogEvent?) {
        if (event != null && calendarDialogUtils != null) {
            calendarDialogUtils!!.initPostDialog(
                mContext,
                event.postSourceType,
                UserInfo.instance().uid
            )
        }
    }

    /**
     * 检查单列表刷新
     * @param event
     * */
    fun onEventMainThread(event: InspectionRefreshEvent) {
        when (event.optType) {
            TaskStateConstant.OPT_TYPE_FINISH,
            TaskStateConstant.OPT_TYPE_DEL,
                -> {
                presenter?.getCalendarToolList()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mOnSwitchBabyCallback != null) {
            BaByInfo.instance().setSwitchBabyCallback(null)
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        this.hidden = hidden
    }

    companion object {

        private const val ARGS_IS_FROM_ACTIVITY = "isFromActivity"
        private const val ARGS_ALERT_ENUM = "alertEnum"

        @JvmStatic
        @JvmOverloads
        fun newInstance(
            isFromActivity: Boolean,
            alertEnum: CalendarAlertEnum = CalendarAlertEnum.NONE,
        ): CalendarFragment {
            val calendarFragment = CalendarFragment()
            val bundle = Bundle()
            bundle.putBoolean(ARGS_IS_FROM_ACTIVITY, isFromActivity)
            bundle.putSerializable(ARGS_ALERT_ENUM, alertEnum)
            calendarFragment.arguments = bundle
            return calendarFragment
        }

        private const val LOGIN = 0x02
    }
}