kotlin version: 2.1.20
error message: java.lang.OutOfMemoryError: GC overhead limit exceeded
	at java.base/java.util.LinkedHashMap.newNode(Unknown Source)
	at java.base/java.util.HashMap.putVal(Unknown Source)
	at java.base/java.util.HashMap.put(Unknown Source)
	at java.base/java.util.HashSet.add(Unknown Source)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolver$collectOverrides$1.invokeSuspend(SpecialFakeOverrideSymbolsResolver.kt:127)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlin.sequences.SequenceBuilderIterator.hasNext(SequenceBuilder.kt:129)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolver.processDeclaration(SpecialFakeOverrideSymbolsResolver.kt:121)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolver.processClass(SpecialFakeOverrideSymbolsResolver.kt:112)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolver.access$processClass(SpecialFakeOverrideSymbolsResolver.kt:41)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolver.getReferencedSimpleFunction(SpecialFakeOverrideSymbolsResolver.kt:292)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitCall(SpecialFakeOverrideSymbolsResolver.kt:177)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitCall(IrElementVisitorVoid.kt:375)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitCall(SpecialFakeOverrideSymbolsResolver.kt:155)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitCall(SpecialFakeOverrideSymbolsResolver.kt:155)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.ir.expressions.IrBranch.acceptChildren(IrBranch.kt:32)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorsKt.acceptChildrenVoid(IrVisitors.kt:19)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitElement(SpecialFakeOverrideSymbolsResolver.kt:163)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitBranch(IrElementVisitorVoid.kt:723)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitBranch(SpecialFakeOverrideSymbolsResolver.kt:155)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitElseBranch(IrElementVisitorVoid.kt:731)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitElseBranch(SpecialFakeOverrideSymbolsResolver.kt:155)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitElseBranch(IrElementVisitorVoid.kt:727)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitElseBranch(SpecialFakeOverrideSymbolsResolver.kt:155)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitElseBranch(SpecialFakeOverrideSymbolsResolver.kt:155)
	at org.jetbrains.kotlin.ir.expressions.IrElseBranch.accept(IrElseBranch.kt:19)
	at org.jetbrains.kotlin.ir.expressions.IrWhen.acceptChildren(IrWhen.kt:27)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorsKt.acceptChildrenVoid(IrVisitors.kt:19)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitElement(SpecialFakeOverrideSymbolsResolver.kt:163)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitExpression(IrElementVisitorVoid.kt:211)
	at org.jetbrains.kotlin.backend.common.actualizer.SpecialFakeOverrideSymbolsResolverVisitor.visitExpression(SpecialFakeOverrideSymbolsResolver.kt:155)


