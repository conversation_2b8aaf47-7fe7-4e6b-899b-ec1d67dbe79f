package cn.mama.pregnant.module.calendar.constant

import base.mvp.BaseContract
import cn.mama.pregnant.business.base.ILoadingDialog
import cn.mama.pregnant.business.bean.calendar.TemperatureServerBean
import cn.mama.pregnant.widget.view.recycleview.bean.RecyclerViewBean

/**
 * 月经记录汇总Contract
 *
 * <AUTHOR>
 * @date 2021/9/2
 */
interface MenstrualReportContract {

    interface View : BaseContract.BaseView, ILoadingDialog {


        /** 刷新整体列表数据 */
        fun refreshTotalData(dataList: MutableList<RecyclerViewBean>)

        /**
         * 无经期记录弹窗
         */
        fun showEmptyMenstrualDialog()

        /**跳转到经期列表*/
        fun gotoPeriodList()

        /**跳转到体温图表*/
        fun gotoTemperatureChart()

        /**跳转到排卵试纸*/
        fun gotoOvulationTestStrip()

        /**跳转到同房列表*/
        fun gotoMakeLove()

        /**跳转到白带列表*/
        fun gotoWhites()

        /** 跳转到通用记体重-图表 */
        fun goToCommonWeightChart()

        /** 跳转到叶酸详情 */
        fun gotoFolicAcidDetails()

        /** 刷新温度信息 */
        fun refreshTemperatureInfo(temperature: TemperatureServerBean?)

    }

    interface Presenter : BaseContract.BasePresenter<View> {
        /**
         * 获取记录汇总数据
         */
        fun fetchData()

        /**
         * 获取体温数据
         */
        fun fetchTemperatureChartData(slide: String)

        /**处理经期板块点击事件*/
        fun handlePeriodBlockClickEvent()

        /**处理体温板块点击事件*/
        fun handleTemperatureBlockClickEvent()

        /**处理排卵试纸板块点击事件*/
        fun handleOvulationTestStripBlockClickEvent()

        /**处理同房板块点击事件*/
        fun handleMakeLoveBlockClickEvent()

        /**处理白带板块点击事件*/
        fun handleWhitesBlockClickEvent()

        /**处理补叶酸板块点击事件*/
        fun handleFolicAcidBlockClickEvent()

    }
}