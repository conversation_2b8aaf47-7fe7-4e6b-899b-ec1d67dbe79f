package cn.mama.pregnant.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import cn.jzvd.JZVideoPlayerStandard;
import cn.mama.pregnant.R;
import cn.mama.pregnant.business.base.BaseMvpActivity;
import cn.mama.pregnant.business.util.expand.StringExpandKt;
import cn.mama.pregnant.business.video.VideoCacheManager;
import cn.mama.pregnant.business.video.util.JzVideoUtil;
import cn.mama.pregnant.view.video.VideoDetailPlayer;
import com.lzx.musiclib.utils.PlayHelper;
import image.GlideLoader;

/**
 * 视频播放界面
 *
 * <AUTHOR>
 * @date 2018/3/7
 */

public class VideoDetailActivity extends BaseMvpActivity {


    public static void launch(Context context, String videoUrl, String videoTitle,
        String videoCover) {
        Intent intent = new Intent(context, VideoDetailActivity.class);
        intent.putExtra("videoUrl", videoUrl);
        intent.putExtra("videoTitle", videoTitle);
        intent.putExtra("videoCover", videoCover);
        context.startActivity(intent);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_video_detail;
    }


    @Override
    protected void init(Bundle savedInstanceState) {
//        SystemBarHelper.hideStatusBar(getWindow(), false);
        VideoDetailPlayer mPlayer = findViewById(R.id.video_player);
        mPlayer.setBackClickListener(() -> finish());

        PlayHelper.setFloatViewVisible(false);

        String videoUrl = getIntent().getStringExtra("videoUrl");
        String videoTitle = getIntent().getStringExtra("videoTitle");
        String videoCover = getIntent().getStringExtra("videoCover");

        try {
            if (!TextUtils.isEmpty(videoUrl)) {
                String proxyUrl = videoUrl;
                if (StringExpandKt.startsWithHTTP(videoUrl)) {
                    //服务器地址需要转换格式
                    proxyUrl = VideoCacheManager.getInstance().getProxyUrl(videoUrl);
                }

                mPlayer.setUp(proxyUrl, JZVideoPlayerStandard.SCREEN_WINDOW_NORMAL,
                    TextUtils.isEmpty(videoTitle) ? "" : videoTitle);
            }
            if (!TextUtils.isEmpty(videoCover)) {
                GlideLoader.with(this, mPlayer.thumbImageView).load(videoCover);
            }
            if (!TextUtils.isEmpty(videoUrl)) {
                mPlayer.startVideo();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    protected boolean enableBackPressedDispatcher() {
        return true;
    }

    @Override
    protected void handleBackPressed() {
        if (JzVideoUtil.backPress()) {
            finish();
            return;
        }
        super.handleBackPressed();
    }

    @Override
    protected void onPause() {
        super.onPause();
        JzVideoUtil.releaseAllVideos();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        JzVideoUtil.releaseCurrentPageVideos(this);
    }

}
