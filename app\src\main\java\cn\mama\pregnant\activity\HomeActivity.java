package cn.mama.pregnant.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.drawerlayout.widget.DrawerLayout.SimpleDrawerListener;
import androidx.fragment.app.Fragment;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.mama.babyrecord.mouble.main.fragment.BabyRecordFragment;
import com.mama.babyrecord.mouble.main.fragment.PregnancyFragment;
import com.mama.babyrecord.mouble.preview.activity.PreviewActivity;
import com.tencent.bugly.crashreport.BuglyLog;

import java.util.ArrayList;

import base.mvp.factory.CreatePresenter;
import cn.mama.pregnancy.community.fragment.topic.NewTopicFragment;
import cn.mama.pregnancy.user.baby.view.PositivePregnancyDialog;
import cn.mama.pregnancy.user.user.event.BottomIconEvent;
import cn.mama.pregnant.BuildConfig;
import cn.mama.pregnant.R;
import cn.mama.pregnant.business.account.bean.UserInfo;
import cn.mama.pregnant.business.bean.home.BottomMenu;
import cn.mama.pregnant.business.bean.home.HomeExtra;
import cn.mama.pregnant.business.consts.HomeConstant;
import cn.mama.pregnant.business.consts.IGlobalRouteProviderConsts;
import cn.mama.pregnant.business.event.HomePageChangedEvent;
import cn.mama.pregnant.business.event.PregnancyModeEvent;
import cn.mama.pregnant.business.helper.statistics.IgnoreStatistics;
import cn.mama.pregnant.business.util.AppUtil;
import cn.mama.pregnant.business.util.LogUtil;
import cn.mama.pregnant.business.util.bottom.BottomMenuType;
import cn.mama.pregnant.business.view.ExitAppSelectDialog;
import cn.mama.pregnant.business.view.ExitAppSelectDialog.DialogListener;
import cn.mama.pregnant.business.view.tab.TabButton;
import cn.mama.pregnant.module.calendar.fragment.CalendarFragment;
import cn.mama.pregnant.module.discovery.DiscoveryFragment;
import cn.mama.pregnant.module.home.activity.BaseHomeActivity;
import cn.mama.pregnant.module.home.contract.MaHomeContract;
import cn.mama.pregnant.module.home.fragment.EmtryFragment;
import cn.mama.pregnant.module.home.fragment.HomeFragment;
import cn.mama.pregnant.module.home.interfaces.IHomeFragment;
import cn.mama.pregnant.module.home.presenter.MaHomePresenter;
import cn.mama.pregnant.module.home.view.drawer.HomeDrawerView;
import cn.mama.pregnant.module.setting.event.SilentDownLoadSuccessEvent;
import util.CollectionsUtils;

/**
 * <AUTHOR>
 */
@IgnoreStatistics
@Route(path = IGlobalRouteProviderConsts.HOME_MAMA_PATH)
@CreatePresenter(MaHomePresenter.class)
public class HomeActivity extends BaseHomeActivity<MaHomeContract.View, MaHomeContract.Presenter>
    implements MaHomeContract.View {

    private HomeDrawerView mHomeDrawerView;
    private PregnancyFragment mPregnancyFragment;
    private DiscoveryFragment mDiscoveryFragment;
    private CalendarFragment mCalendarFragment;
    private BabyRecordFragment mBabyRecordFragment;
    private TabButton mTabDiscover;
    private TabButton mTabHome, mTabTopic, mTabRecord, mTabTreebee;
    private ExitAppSelectDialog installDialog;

    /**
     * 登陆后跳备孕转到日历或者备孕选中日历tab ps:该方法因为FLAG_ACTIVITY_CLEAR_TASK标记，所以activity不会复用，会重新创建
     */
    public static void invokeToCalendar(Context ctx, boolean switchCalendar) {
        Intent i = HomeIntentKt.createHomeIntent(ctx, HomeConstant.TO_HOME_PAGE, switchCalendar);
        i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        ctx.startActivity(i);
    }

    /**
     * 单例模式启动activity ps:该方法因为FLAG_ACTIVITY_CLEAR_TASK标记，所以activity不会复用，会重新创建
     * 如果需要复用Activity，请使用{@link
     * cn.mama.pregnant.provider.IMainPublicProviderImpl#launchHome(Context, int)}
     */
    public static void invoke(Context ctx, int index) {
        Intent i = HomeIntentKt.createHomeIntent(ctx, index);
        i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        ctx.startActivity(i);
    }

    /**
     * 单例模式启动activity
     */
    public static void invoke(Context ctx, int index, HomeExtra homeExtra) {
        Intent i = HomeIntentKt.createHomeIntent(ctx, index, false, homeExtra);
        ctx.startActivity(i);
    }

    public static void invoke(Context context, HomeExtra homeExtra) {
        Intent i = HomeIntentKt
            .createHomeIntent(context, HomeConstant.TO_HOME_PAGE, false, homeExtra);
        i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(i);
    }

    public static void invoke(Context ctx) {
        invoke(ctx, 0);
    }

    @SuppressLint("CheckResult")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            savedInstanceState.putParcelable("android:support:fragments", null);
        }
        context = this;
        BuglyLog.d(TAG, "before home activity onCreate");
        super.onCreate(savedInstanceState);
        BuglyLog.d(TAG, "after home activity onCreate");
        if (UserInfo.instance().isBaba()) {
            BuglyLog.d(TAG, "current is Baba should switch to mama");
            HomeActivity.invoke(this);
            finish();
            return;
        }
        getPresenter().onCreate(this, getIntent());
    }

    @Override
    protected void onStart() {
        super.onStart();
        LogUtil.d("启动优化", "首页可见");
    }

    @Override
    protected void onResume() {
        super.onResume();
        //进入备孕测试H5页面
        if (getPresenter() != null) {
            getPresenter().onResume(this);
        }

    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_home;
    }

    @SuppressLint("CheckResult")
    @Override
    protected void init(Bundle savedInstanceState) {
        super.init(savedInstanceState);

        initView();
        initData();
        initFragment();
        resettingTabButton();
        updateFragment(false, homeExtra);
        initBottomIcon();
        getTag(true, true);

    }

    @Override
    protected void initData() {
        super.initData();
        firstGetHomeDrawerData();
        mTabsOriginal = new ArrayList<>();
        mTabsOriginal.add(mTabHome);
        mTabsOriginal.add(mTabTopic);
        mTabsOriginal.add(mTabTreebee);
        mTabsOriginal.add(mTabRecord);
        mTabsOriginal.add(mTabDiscover);
    }

    /**
     * 进入首页请求一次抽屉数据，避免首次打开抽屉空白，以及通知红点刷新。然后标记为脏数据，下次打开抽屉再次请求最新数据
     */
    private void firstGetHomeDrawerData() {
        getHomeDrawerData(false);
        markHomeDrawerDataDirty();
    }

    @Override
    protected void initView() {
        super.initView();

        RelativeLayout rlHomeContainer = findViewById(R.id.rl_home_container);
        mDrawerLayout = findViewById(R.id.drawer_layout);
        mHomeDrawerView = findViewById(R.id.home_drawer);
        mHomeDrawerView.init(getLifecycle(), drawerGravity);
        mTabHome = findViewById(R.id.homepage);
        mTabTopic = findViewById(R.id.topic);
        mTabRecord = findViewById(R.id.record);
        mTabTreebee = findViewById(R.id.treebee);
        mTabDiscover = findViewById(R.id.discover);

        mDrawerLayout.addDrawerListener(createDrawerListenerForTabTipsVisibility());
        mDrawerLayout.addDrawerListener(createDrawerListenerForHomeContainer(rlHomeContainer));
        mDrawerLayout.addDrawerListener(new SimpleDrawerListener() {
            @Override
            public void onDrawerSlide(View drawerView, float slideOffset) {
                super.onDrawerSlide(drawerView, slideOffset);
                if (slideOffset == 0.0) {
                    markHomeDrawerDataDirty();
                } else if (isHomeDrawerDataDirty()) {
                    getHomeDrawerData(true);
                }
            }
        });
    }

    //region 初始化fragment
    @Override
    public void initFragment() {
        super.initFragment();

        //首页
        mHomeFragment = HomeFragment.newInstance();

        //圈子
        mTopicFragment = NewTopicFragment.newInstance();
        //发现
        mDiscoveryFragment = DiscoveryFragment.newInstance();

        //日历
        mCalendarFragment = CalendarFragment.newInstance(false);
        //小记信息流
        mBabyRecordFragment = BabyRecordFragment.Companion.newInstance(0,
            UserInfo.instance().getBBid());

    }


    @Override
    public void onActivityReenter(int resultCode, Intent data) {
        super.onActivityReenter(resultCode, data);
        if (mBabyRecordFragment != null && mBabyRecordFragment.isVisible() && data != null) {
            String fileKey = data.getStringExtra(PreviewActivity.INTENT_SHARED_ELEMENT_FILE_KEY);
            mBabyRecordFragment.onActivityReenter(fileKey);
        }
    }


    @Override
    protected void onCustomNewIntent(Intent intent) {
        super.onCustomNewIntent(intent);
        getPresenter().onCustomNewIntent(this, intent);

    }


    @Override
    protected void onDestroy() {
        if (getPresenter() != null) {
            getPresenter().onDestroy(this);
        }
        super.onDestroy();
    }

    @Override
    public void setupInitialise() {
        super.setupInitialise();
        //纪录
        mDiscoveryFragment = null;
        mPregnancyFragment = null;
    }


    /**
     * 重置到首页的选择 （嗯）
     */
    public void onEventMainThread(BottomIconEvent event) {
        switchFragment(getDynamicHomeIndex());
    }

    @Override
    protected boolean getHomeMode() {
        return UserInfo.instance().isMaMa();
    }


    /**
     * 切换积极备孕弹窗
     *
     * @param event 事件
     */
    public void onEventMainThread(PregnancyModeEvent event) {
        if (!this.isFinishing()) {
            new PositivePregnancyDialog(this, event.getTitle(), event.getDesc(),
                event.getPic()).show();
        }
    }

    /**
     * 切换tab 模式 响应 加lock 控制
     */
    public void onEventMainThread(HomePageChangedEvent event) {
        onHomePageChangedEventd(event);

        synchronized (this) {
            switch (event.getIndex()) {
                //单纯跳首页 不做任何操作
                case HomeConstant.HOME:
                    switchFragment(getDynamicHomeIndex());
                    return;
                case HomeConstant.PTREADY:
                    if (mCalendarFragment != null) {
                        mCalendarFragment.refreshDates();
                    }
                    if (mHomeFragment != null && !CollectionsUtils.isEmpty(fragments)) {
                        mHomeFragment = HomeFragment.newInstance();
                        if (fragments.size() > getDynamicHomeIndex()) {
                            fragments.set(getStageManagerIndex(), mHomeFragment);
                        }
                    }
                    return;
                case HomeConstant.PAGE_HOME:
                    switchFragment(getDynamicHomeIndex());
                    break;
                case HomeConstant.PAGE_RECORD:
                case HomeConstant.RECORD:
                    switchFragment(getDynamicHomeIndex());
                    break;
                case HomeConstant.CALENDAR:
                    if (mCalendarFragment != null) {
                        mCalendarFragment.setAlertEnum(event.getCalendarAlertEnum());
                        mCalendarFragment.showAlertIfNeed();
                    }
                    break;
                default:
                    break;
            }
        }
    }


    /**
     * 通过type获取fragment
     */
    @Override
    public Fragment getFragmentByType(String type, BottomMenu bottomMenu) {
        if (TextUtils.isEmpty(type)) {
            return null;
        }

        switch (type) {
            case BottomMenuType.HOME:
                return mHomeFragment;
            case BottomMenuType.THREAD:
                return mTopicFragment;
            case BottomMenuType.WAP:
                return new EmtryFragment();
            case BottomMenuType.TOOL:
                return mDiscoveryFragment;
            case BottomMenuType.MALL:
                return new EmtryFragment();
            case BottomMenuType.CALENDAR:
                return mCalendarFragment;

            case BottomMenuType.XJ:
                return mBabyRecordFragment;
            case BottomMenuType.PREGNANCY:
                if (mPregnancyFragment == null) {
                    mPregnancyFragment = PregnancyFragment.Companion.newInstance(
                        UserInfo.instance().getBBid(), false);
                }
                return mPregnancyFragment;
            default:
        }
        return null;

    }

    @Override
    protected int getDynamicHomeIndex() {
        if (homeTabIndex != -1) {
            return homeTabIndex;
        }
        return super.getDynamicHomeIndex();
    }

    @Override
    protected void getHomeDrawerData(boolean registerReportEvent) {
        super.getHomeDrawerData(registerReportEvent);
        mHomeDrawerView.refreshData(registerReportEvent);
    }

    @Override
    public int getRefreshRes() {
        return cn.mama.pregnant.business.R.drawable.common_icon_tab_btt_sel_ma;
    }

    @Override
    protected void setCircleRefresh() {
        if (mTopicFragment != null) {
            mTopicFragment.onRefreshCurrentFragment();
        }
    }

    @Override
    public void onRequestChanged(int requestCount) {

    }

    @Override
    public void onFeedChanged(int feed) {
        setTopFragmentReadCount();
    }

    @Override
    public void onNotifyChanged(int notifyCount) {
        setTopFragmentReadCount();
    }

    @Override
    public void onPmChanged(int pmCount) {

        setTopFragmentReadCount();
    }

    private void setTopFragmentReadCount() {
        if (!UserInfo.instance().isLogin()) {
            return;
        }

        if (mTabTopic != null) {
            mTabTopic.setTipOn(false);
        }

    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mBabyRecordFragment != null && mBabyRecordFragment.isVisible()) {
            mBabyRecordFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (mPregnancyFragment != null && mPregnancyFragment.isVisible()) {
            mPregnancyFragment.onActivityResult(requestCode, resultCode, data);
        }
    }


    @Override
    protected void scrollToTopOnTab() {
        super.scrollToTopOnTab();
        if (mHomeFragment instanceof IHomeFragment) {
            ((IHomeFragment) mHomeFragment).scrollToTopForRefresh();
        }
    }


    @Override
    public void onNewIntentCalendar(int tabIndex, HomeExtra homeExtra) {
        switchFragmentByHomeExtra(tabIndex, homeExtra);
    }

    /**
     * 有新版本，且安装包已下载完毕，唤起安装界面
     */
    public void onEventMainThread(SilentDownLoadSuccessEvent event) {
        if (!this.isFinishing()) {
            showUpdateDialog(event);
        }
    }


    /**
     * 安装弹窗
     */
    private void showUpdateDialog(SilentDownLoadSuccessEvent event) {
        installDialog = new ExitAppSelectDialog(this, new DialogListener() {
            @Override
            public void SureListener() {
                installDialog.dissmiss();
                AppUtil.installApk(event.getUri(), BuildConfig.APPLICATION_ID, mContext);
            }

            @Override
            public void DismissListener() {
                installDialog.dissmiss();
            }
        });
        installDialog.initDialog("已在wifi环境下载新的安装包，是否进行安装", "确定",
            getString(cn.mama.pregnant.business.R.string.cancel));
    }


}
