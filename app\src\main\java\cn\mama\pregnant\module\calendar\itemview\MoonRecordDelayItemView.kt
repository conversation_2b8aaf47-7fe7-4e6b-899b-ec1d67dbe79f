package cn.mama.pregnant.module.calendar.itemview

import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.mama.pregnant.business.bean.discovery.Discovery
import cn.mama.pregnant.business.router.RouteServiceManager
import cn.mama.pregnant.business.util.monitor.ItemViewMonitorHelper
import cn.mama.pregnant.business.view.ItemViewBindingDelegate
import cn.mama.pregnant.databinding.ItemMoonRecordDelayBinding
import cn.mama.pregnant.databinding.LayoutLastMenstruationChildBinding
import cn.mama.pregnant.module.calendar.bean.MenstruationDelayReason
import cn.mama.pregnant.widget.view.recycleview.base.ViewHolder
import cn.mama.pregnant.widget.view.recycleview.bean.RecyclerViewBean
import com.blankj.utilcode.util.ScreenUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.fondesa.recyclerviewdivider.dividerBuilder
import util.dp


/**
 * 14.8.0:增加经期推迟原因 itemView
 *
 * <AUTHOR>
 * @date 2024/09/02
 */
class MoonRecordDelayItemView(private val lifecycle: Lifecycle) :
    ItemViewBindingDelegate<RecyclerViewBean, ItemMoonRecordDelayBinding>() {

    private val itemViewMonitorHelper: ItemViewMonitorHelper by lazy {
        ItemViewMonitorHelper(lifecycle)
    }

    override fun isForViewType(item: RecyclerViewBean, position: Int): Boolean {
        return item.type == CollectItemType.COLLECT_MOON_RECORD_DELAY
    }

    override fun initData(
        holder: ViewHolder,
        item: RecyclerViewBean,
        position: Int,
        binding: ItemMoonRecordDelayBinding,
    ) {
        val entity = item.data as? MenstruationDelayReason ?: return
        val context = holder.itemView.context

        binding.tvDelayTips.text = entity.delayReasonDesc.orEmpty()

        val rvDelayReason = binding.rvDelayReason
        var adatper = rvDelayReason.adapter as? DelayReasonChildAdapter
        val entrancesList = entity.itemList.orEmpty().toMutableList()
        if (adatper == null) {
            adatper = DelayReasonChildAdapter(lifecycle, entrancesList)
            rvDelayReason.adapter = adatper
            rvDelayReason.layoutManager =
                CustomLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            rvDelayReason.addDivider()
        } else {
            adatper.setNewInstance(entrancesList)
        }
    }

    override fun initListener(
        holder: ViewHolder,
        item: RecyclerViewBean,
        position: Int,
        binding: ItemMoonRecordDelayBinding,
    ) {
        //do nothing
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        itemViewMonitorHelper.unregisterView(holder)
    }

    private fun RecyclerView.addDivider() {
        context.dividerBuilder().color(Color.TRANSPARENT).showFirstDivider().showLastDivider()
            .sizeProvider { _, divider, _ ->
                if (divider.isFirstDivider || divider.isLastDivider) 20F.dp() else if (divider.orientation.isVertical) 10F.dp() else 0
            }.build().addTo(this)
    }

    private class CustomLayoutManager(val context: Context,orientation:Int,reverseLayout:Boolean) : LinearLayoutManager(context,orientation,reverseLayout) {

        var maxHeight=0

        override fun onLayoutChildren(
            recycler: RecyclerView.Recycler?,
            state: RecyclerView.State?,
        ) {
            super.onLayoutChildren(recycler, state)
            for (i in 0 until childCount) {
                val child = getChildAt(i)
                if (child != null) {
                    val newHeight: Int = calculateHeightForChild(child)
                    val layoutParams: ViewGroup.LayoutParams = child.getLayoutParams()
                    layoutParams.height = newHeight
                    child.setLayoutParams(layoutParams)
                }
            }
        }

        override fun onMeasure(
            recycler: RecyclerView.Recycler,
            state: RecyclerView.State,
            widthSpec: Int,
            heightSpec: Int
        ) {

            super.onMeasure(recycler, state, widthSpec, heightSpec)
        }

        fun calculateHeightForChild(child: View):Int {
            // 根据子视图计算高度的逻辑
            // 动态计算 TextView 的高度
            val widthSpec = View.MeasureSpec.makeMeasureSpec(child.width, View.MeasureSpec.AT_MOST)
            val heightSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            child.measure(widthSpec, heightSpec)
            val height = child.measuredHeight
            if(maxHeight<height){
                maxHeight=height
            }
            return maxHeight
        }

    }

    private class DelayReasonChildAdapter(
        val lifecycle: Lifecycle,
        discoveries: MutableList<Discovery>,
    ) : BaseQuickAdapter<Discovery, BaseViewHolder>(
        cn.mama.pregnant.R.layout.layout_last_menstruation_child,
        discoveries,
    ) {
        private val itemViewMonitorHelper: ItemViewMonitorHelper by lazy {
            ItemViewMonitorHelper(lifecycle)
        }

        override fun convert(holder: BaseViewHolder, item: Discovery) {
            val childBinding = LayoutLastMenstruationChildBinding.bind(holder.itemView)
            val layoutParams= LinearLayout.LayoutParams(
                ScreenUtils.getScreenWidth() / 2, LinearLayout.LayoutParams.MATCH_PARENT
            )
            childBinding.llFeedBg.layoutParams =layoutParams
            val content = item.title + ":" + item.content
            val colorSpan = ForegroundColorSpan(
                ContextCompat.getColor(
                    context, cn.mama.pregnant.business.R.color.CT1
                )
            )
            val builder = SpannableStringBuilder(content)
            builder.setSpan(colorSpan, 0, item.title.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            childBinding.tvFeedFunction.text = builder
            holder.itemView.requestLayout()
            itemViewMonitorHelper.registerView(holder.itemView, item.report_event)
        }

        override fun onViewRecycled(holder: BaseViewHolder) {
            super.onViewRecycled(holder)
            itemViewMonitorHelper.unregisterView(holder)
        }

    }

}