package cn.mama.pregnant.module.calendar.bean

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * mar
 * 2019/3/1
 * <AUTHOR>
 */
@Parcelize
data class TipsData(
    var desc //提示文案
    : String? = null,

    @SerializedName("open_type")
    var openType: Int //跳转链接
    = 0,

    @SerializedName("intro_link")
    var introLink //姨妈介绍
    : String? = null
) : Parcelable