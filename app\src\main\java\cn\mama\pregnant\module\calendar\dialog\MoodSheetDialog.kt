package cn.mama.pregnant.module.calendar.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.mama.adsdk.bean.AdModelBean
import cn.mama.pregnant.R
import cn.mama.pregnant.business.bean.calendar.MoodBean
import cn.mama.pregnant.business.util.ToastUtil
import cn.mama.pregnant.business.view.CustomAdvertView
import cn.mama.pregnant.business.view.MultiItemTypeAdapter
import cn.mama.pregnant.module.calendar.itemview.CalendarToolMoodItemView
import com.google.android.material.bottomsheet.BottomSheetDialog
import imp.SimpleLineVisibilityListener

/**
 * 心情SheetDialog
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
class MoodSheetDialog(
    context: Context,
    private var selectedMoodId: Int,
    private val moodList: List<MoodBean>,
    private var adModelBean: AdModelBean?
) : BottomSheetDialog(context) {

    private val ivCancel: ImageView
    private val rvMood: RecyclerView
    private val tvSave: TextView
    private val cavBanner : CustomAdvertView

    var onSaveListener: ((mood: MoodBean?) -> Unit)? = null

    init {
        val rootView = LayoutInflater.from(context)
            .inflate(R.layout.dialog_mood_sheet, null)
        setContentView(rootView)
        ivCancel = rootView.findViewById(R.id.iv_cancel)
        rvMood = rootView.findViewById(R.id.rv_mood)
        tvSave = rootView.findViewById(R.id.tv_save)
        cavBanner = rootView.findViewById(R.id.cav_banner)

        initView()
        initData()
        initListener()
    }

    private fun initView() {
        //解决圆角背景 原因：由于它上面蒙了一层布局 design_bottom_sheet是系统的布局，直接找到它，然后给它设全透明就好了
        val bottomSheet =
            delegate.findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.setBackgroundColor(
            ContextCompat.getColor(
                context,
                cn.mama.pregnant.business.R.color.transparent
            )
        )

        rvMood.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
    }

    private fun initData() {
        moodList.forEach { it.isSelect = it.mood != 0 && it.mood == selectedMoodId }
        initAd()
        setupRvAdapter()
    }

    private fun initAd() {
        adModelBean ?: return
        cavBanner.bannerCorners = 8
        cavBanner.bannerPaddingLeftAndRight = 16
        cavBanner.isClose = false
        cavBanner.visibility = View.VISIBLE
        cavBanner.load(adModelBean, CustomAdvertView.STYLE_NOE, true, null)
    }

    private fun initListener() {
        ivCancel.setOnClickListener { dismiss() }
        tvSave.setOnClickListener {
            val selectedMood = moodList.firstOrNull { it.isSelect }
            if (selectedMoodId == 0 && selectedMood == null) {
                ToastUtil.showMsg( "是不是忘了选择心情？")
            } else {
                dismiss()
                onSaveListener?.invoke(selectedMood)
            }
        }
    }

    private fun setupRvAdapter() {
        val adapter = MultiItemTypeAdapter(context, moodList).apply {
            val moodItemView = CalendarToolMoodItemView()
            moodItemView.setVisibilityListener(SimpleLineVisibilityListener(moodList))
            addItemViewDelegate(moodItemView)
        }
        adapter.setOnItemClickListener(object : MultiItemTypeAdapter.OnItemClickListener {

            @SuppressLint("NotifyDataSetChanged")
            override fun onItemClick(view: View, holder: RecyclerView.ViewHolder, position: Int) {
                moodList.forEachIndexed { index, moodBean ->
                    moodBean.isSelect = if (index == position) {
                        !moodBean.isSelect
                    } else {
                        false
                    }
                }
                adapter.notifyDataSetChanged()
            }

            override fun onItemLongClick(
                view: View,
                holder: RecyclerView.ViewHolder,
                position: Int,
            ): Boolean {
                //do nothing
                return false
            }

        })
        rvMood.adapter = adapter
    }

}