package cn.mama.pregnant.module.calendar.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.mama.pregnant.R
import cn.mama.pregnant.business.bean.calendar.SexualWhitesBean
import cn.mama.pregnant.business.view.MultiItemTypeAdapter
import cn.mama.pregnant.module.calendar.itemview.CalendarToolMakeLoveItemView
import com.google.android.material.bottomsheet.BottomSheetDialog
import imp.SimpleLineVisibilityListener

/**
 * 心情SheetDialog
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
class MakeLoveSheetDialog(
    context: Context,
    private val makeLoveList: List<SexualWhitesBean>,
) : BottomSheetDialog(context) {

    private val tvTitle: TextView
    private val ivCancel: ImageView
    private val rvMakeLove: RecyclerView
    private val tvSave: TextView

    var onItemDeleteListener: ((sheetDialog: BottomSheetDialog, bean: SexualWhitesBean?) -> Unit)? =
        null
    var onItemClickListener: ((sheetDialog: BottomSheetDialog, bean: SexualWhitesBean?) -> Unit)? =
        null
    var onItemAddListener: ((sheetDialog: BottomSheetDialog) -> Unit)? = null

    init {
        val rootView = LayoutInflater.from(context)
            .inflate(R.layout.dialog_make_love_sheet, null)
        setContentView(rootView)
        tvTitle = rootView.findViewById(R.id.tv_title)
        ivCancel = rootView.findViewById(R.id.iv_cancel)
        rvMakeLove = rootView.findViewById(R.id.rv_make_love)
        tvSave = rootView.findViewById(R.id.tv_save)

        initView()
        initData()
        initListener()
    }

    private fun initView() {
        //解决圆角背景 原因：由于它上面蒙了一层布局 design_bottom_sheet是系统的布局，直接找到它，然后给它设全透明就好了
        val bottomSheet =
            delegate.findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.setBackgroundColor(
            ContextCompat.getColor(
                context,
                cn.mama.pregnant.business.R.color.transparent
            )
        )

        rvMakeLove.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
    }

    private fun initData() {
        tvTitle.text = if (makeLoveList.isEmpty()) {
            "同房"
        } else {
            val count = makeLoveList.size.toString()
            context.getString(R.string.calendardialog_makelove_title, count)
        }
        setupRvAdapter()
    }

    private fun initListener() {
        ivCancel.setOnClickListener { dismiss() }

        tvSave.setOnClickListener {
            onItemAddListener?.invoke(this)
        }
    }

    private fun setupRvAdapter() {
        val adapter = MultiItemTypeAdapter(context, makeLoveList).apply {
            val makeLoveItemView = CalendarToolMakeLoveItemView { bean ->
                onItemDeleteListener?.invoke(this@MakeLoveSheetDialog, bean)
            }
            makeLoveItemView.setVisibilityListener(SimpleLineVisibilityListener(makeLoveList))
            addItemViewDelegate(makeLoveItemView)
        }
        adapter.setOnItemClickListener(object : MultiItemTypeAdapter.OnItemClickListener {

            @SuppressLint("NotifyDataSetChanged")
            override fun onItemClick(view: View, holder: RecyclerView.ViewHolder, position: Int) {
                val sexualWhitesBean = makeLoveList[position]
                onItemClickListener?.invoke(this@MakeLoveSheetDialog, sexualWhitesBean)
            }

            override fun onItemLongClick(
                view: View,
                holder: RecyclerView.ViewHolder,
                position: Int,
            ): Boolean {
                return false
            }

        })
        rvMakeLove.adapter = adapter
    }

}