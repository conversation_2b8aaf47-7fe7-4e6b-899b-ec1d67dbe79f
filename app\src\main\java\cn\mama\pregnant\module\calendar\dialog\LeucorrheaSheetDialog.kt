package cn.mama.pregnant.module.calendar.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.mama.adsdk.bean.AdModelBean
import cn.mama.pregnant.R
import cn.mama.pregnant.business.bean.calendar.SexualWhitesBean
import cn.mama.pregnant.business.router.RouteServiceManager
import cn.mama.pregnant.business.util.ToastUtil
import cn.mama.pregnant.business.view.CustomAdvertView
import cn.mama.pregnant.business.view.MultiItemTypeAdapter
import cn.mama.pregnant.module.calendar.constant.UrlPath
import cn.mama.pregnant.module.calendar.itemview.CalendarToolLeucorrheaItemView
import com.google.android.material.bottomsheet.BottomSheetDialog

/**
 * 心情SheetDialog
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
class LeucorrheaSheetDialog(
    context: Context,
    private var selectedId: Int,
    private val leucorrheaList: List<SexualWhitesBean>,
    private var adModelBean: AdModelBean?
) : BottomSheetDialog(context) {

    private val ivCancel: ImageView
    private val rvLeucorrhea: RecyclerView
    private val tvKnowledge: TextView
    private val tvSave: TextView
    private val cavBanner : CustomAdvertView

    var onSaveListener: ((bean: SexualWhitesBean?) -> Unit)? = null

    init {
        val rootView = LayoutInflater.from(context)
            .inflate(R.layout.dialog_leucorrhea_sheet, null)
        setContentView(rootView)
        ivCancel = rootView.findViewById(R.id.iv_cancel)
        rvLeucorrhea = rootView.findViewById(R.id.rv_leucorrhea)
        tvKnowledge = rootView.findViewById(R.id.tv_knowledge)
        tvSave = rootView.findViewById(R.id.tv_save)
        cavBanner = rootView.findViewById(R.id.cav_banner)

        initView()
        initData()
        initListener()
    }

    private fun initView() {
        //解决圆角背景 原因：由于它上面蒙了一层布局 design_bottom_sheet是系统的布局，直接找到它，然后给它设全透明就好了
        val bottomSheet =
            delegate.findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.setBackgroundColor(
            ContextCompat.getColor(
                context,
                cn.mama.pregnant.business.R.color.transparent
            )
        )

        rvLeucorrhea.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
    }

    private fun initData() {
        leucorrheaList.forEach { it.isSelect = it.leucorrhea != 0 && it.leucorrhea == selectedId }
        initAd()
        setupRvAdapter()
    }

    private fun initAd() {
        adModelBean ?: return
        cavBanner.bannerCorners = 8
        cavBanner.bannerPaddingLeftAndRight = 16
        cavBanner.isClose = false
        cavBanner.visibility = View.VISIBLE
        cavBanner.load(adModelBean, CustomAdvertView.STYLE_NOE, true, null)
    }

    private fun initListener() {
        ivCancel.setOnClickListener { dismiss() }

        tvKnowledge.setOnClickListener {
            RouteServiceManager.getInstance().mainPublicProvider?.goToCommonWebActivity(context,UrlPath.LEUCORRHEA_DETAIL_URL,null)
            dismiss()
        }

        tvSave.setOnClickListener {
            val selectedMood = leucorrheaList.firstOrNull { it.isSelect }
            if (selectedMood == null && selectedId == 0) {
                ToastUtil.showMsg( "是不是忘了选择白带？")
            } else {
                dismiss()
                onSaveListener?.invoke(selectedMood)
            }
        }
    }

    private fun setupRvAdapter() {
        val adapter = MultiItemTypeAdapter(context, leucorrheaList).apply {
            addItemViewDelegate(CalendarToolLeucorrheaItemView())
        }
        adapter.setOnItemClickListener(object : MultiItemTypeAdapter.OnItemClickListener {

            @SuppressLint("NotifyDataSetChanged")
            override fun onItemClick(view: View, holder: RecyclerView.ViewHolder, position: Int) {
                leucorrheaList.forEachIndexed { index, bean ->
                    bean.isSelect = if (index == position) {
                        !bean.isSelect
                    } else {
                        false
                    }
                }
                adapter.notifyDataSetChanged()
            }

            override fun onItemLongClick(
                view: View,
                holder: RecyclerView.ViewHolder,
                position: Int,
            ): Boolean {
                return false
            }

        })
        rvLeucorrhea.adapter = adapter
    }

}