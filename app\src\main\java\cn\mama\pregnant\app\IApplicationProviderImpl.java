package cn.mama.pregnant.app;

import android.app.Application.ActivityLifecycleCallbacks;
import android.content.Context;
import cn.mama.MyApplication;
import cn.mama.pregnant.business.provider.IApplicationProvider;
import cn.mama.pregnant.business.consts.IGlobalRouteProviderConsts;
import com.alibaba.android.arouter.facade.annotation.Route;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Route(path = IGlobalRouteProviderConsts.APPLIATION_SERVICE)
public class IApplicationProviderImpl implements IApplicationProvider {
    private Context context;


    @Override
    public void init(Context context) {
        this.context = context;
    }

    @Override
    public Context getAppContext() {
        return context;
    }

    @Override
    public Map<String, String> getExtraParamMap() {
        //MyApplication.getExtraParamMap 方法中，这个map是在Arouter.init()方法后才赋值的
        return MyApplication.getExtraParamMap();
    }

    @Override
    public String getPlatform_id() {
        return MyApplication.platform_id;
    }

    @Override
    public void registerActivityLifecycleCallbacks(ActivityLifecycleCallbacks callback) {
        MyApplication.getAppContext().registerActivityLifecycleCallbacks(callback);
    }

    @Override
    public void unregisterActivityLifecycleCallbacks(ActivityLifecycleCallbacks callback) {
        MyApplication.getAppContext().unregisterActivityLifecycleCallbacks(callback);
    }
}
