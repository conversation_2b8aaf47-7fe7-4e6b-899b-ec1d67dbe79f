package cn.mama.pregnant.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.drawerlayout.widget.DrawerLayout.SimpleDrawerListener;
import androidx.fragment.app.Fragment;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.ActivityUtils;
import com.mama.babyrecord.mouble.main.fragment.BabyRecordFragment;
import com.mama.babyrecord.mouble.main.fragment.PregnancyFragment;
import com.mama.babyrecord.widget.RecordSelectDialog;

import java.util.ArrayList;

import base.mvp.factory.CreatePresenter;
import cn.mama.pregnancy.community.fragment.topic.NewTopicFragment;
import cn.mama.pregnancy.user.baby.view.PositivePregnancyDialog;
import cn.mama.pregnancy.user.user.LoginActivity;
import cn.mama.pregnant.R;
import cn.mama.pregnant.business.account.bean.UserInfo;
import cn.mama.pregnant.business.bean.home.BottomMenu;
import cn.mama.pregnant.business.bean.home.HomeExtra;
import cn.mama.pregnant.business.consts.HomeConstant;
import cn.mama.pregnant.business.consts.IGlobalRouteProviderConsts;
import cn.mama.pregnant.business.event.HomePageChangedEvent;
import cn.mama.pregnant.business.event.PregnancyModeEvent;
import cn.mama.pregnant.business.util.bottom.BottomMenuType;
import cn.mama.pregnant.business.view.tab.TabButton;
import cn.mama.pregnant.module.calendar.fragment.CalendarFragment;
import cn.mama.pregnant.module.discovery.DiscoveryFragment;
import cn.mama.pregnant.module.home.activity.BaseHomeActivity;
import cn.mama.pregnant.module.home.contract.DaHomeContract;
import cn.mama.pregnant.module.home.fragment.EmtryFragment;
import cn.mama.pregnant.module.home.fragment.HomeFragment;
import cn.mama.pregnant.module.home.interfaces.IHomeFragment;
import cn.mama.pregnant.module.home.presenter.DaHomePresenter;
import cn.mama.pregnant.module.home.view.drawer.HomeDrawerView;

/**
 * <AUTHOR>
 * @date 11/10/14
 */
@Route(path = IGlobalRouteProviderConsts.HOME_BABA_PATH)
@CreatePresenter(DaHomePresenter.class)
public class DadHomeActivity extends BaseHomeActivity<DaHomeContract.View, DaHomeContract.Presenter>
    implements DaHomeContract.View {

    private HomeDrawerView mHomeDrawerView;

    private TabButton mTabHome;
    private TabButton mTabDiscover;
    private TabButton mTabRecord;
    private TabButton mTabTreebee;
    private TabButton mTabMine;
    private RecordSelectDialog dialog;

    private DiscoveryFragment mDiscoveryFragment;
    private PregnancyFragment mPregnancyFragment;
    private CalendarFragment mCalendarFragment;
    private BabyRecordFragment mBabyRecordFragment;


    /**
     * 单例模式启动activity ps:该方法因为FLAG_ACTIVITY_CLEAR_TASK标记，所以activity不会复用，会重新创建
     *
     * @param ctx 上下文
     */
    public static void invoke(Context ctx) {
        Intent i = HomeIntentKt.createHomeIntent(ctx);
        i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        ctx.startActivity(i);
    }

    @SuppressLint("CheckResult")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            savedInstanceState.putParcelable("android:support:fragments", null);
        }

        super.onCreate(savedInstanceState);
        if (UserInfo.instance().isMaMa()) {
            DadHomeActivity.invoke(this);
            finish();
            return;
        }
        getPresenter().onCreate(this, getIntent());
        context = this;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_dad_home;
    }

    @Override
    protected void init(Bundle savedInstanceState) {
        super.init(savedInstanceState);
        initView();
        initData();

        initFragment();

        resettingTabButton();

        updateFragment(false, homeExtra);
        initBottomIcon();

        getTag(true, true);
    }

    @Override
    protected void initData() {
        super.initData();
        firstGetHomeDrawerData();
        mTabsOriginal = new ArrayList<>();

        mTabsOriginal.add(mTabHome);
        mTabsOriginal.add(mTabTreebee);
        mTabsOriginal.add(mTabRecord);
        mTabsOriginal.add(mTabDiscover);
        mTabsOriginal.add(mTabMine);
    }

    /**
     * 进入首页请求一次抽屉数据，避免首次打开抽屉空白，以及通知红点刷新。然后标记为脏数据，下次打开抽屉再次请求最新数据
     */
    private void firstGetHomeDrawerData() {
        getHomeDrawerData(false);
        markHomeDrawerDataDirty();
    }

    @Override
    protected void initView() {
        super.initView();
        RelativeLayout rlHomeContainer = findViewById(R.id.rl_home_container);
        mDrawerLayout = findViewById(R.id.drawer_layout);
        mHomeDrawerView = findViewById(R.id.home_drawer);
        mHomeDrawerView.init(getLifecycle(), drawerGravity);
        mTabHome = findViewById(R.id.homepage);
        mTabDiscover = findViewById(R.id.discover);
        mTabRecord = findViewById(R.id.record);
        mTabTreebee = findViewById(R.id.treebee);
        mTabMine = findViewById(R.id.mine);

        mDrawerLayout.addDrawerListener(createDrawerListenerForTabTipsVisibility());
        mDrawerLayout.addDrawerListener(createDrawerListenerForHomeContainer(rlHomeContainer));
        mDrawerLayout.addDrawerListener(new SimpleDrawerListener() {
            @Override
            public void onDrawerSlide(View drawerView, float slideOffset) {
                super.onDrawerSlide(drawerView, slideOffset);
                if (slideOffset == 0.0) {
                    markHomeDrawerDataDirty();
                } else if (isHomeDrawerDataDirty()) {
                    getHomeDrawerData(true);
                }
            }
        });
    }

    @Override
    protected void onCustomNewIntent(Intent intent) {
        super.onCustomNewIntent(intent);
        if (getPresenter() != null) {
            getPresenter().onCustomNewIntent(this, intent);
        }
    }


    private void toRecord() {
        //登录状态
        if (UserInfo.instance().isLogin()) {
            //TUID不为空，关联成功的状态
            if (!TextUtils.isEmpty(UserInfo.instance().getmTUid())) {
                switchFragment(1);
            } else {
                //TUID为空，没有关联的状态
                switchFragment(currentTabIndex);
                if (!ActivityUtils.isActivityAlive(this)) {
                    return;
                }
                if (null == dialog) {
                    dialog = new RecordSelectDialog(this);
                }
                dialog.initDialog();
            }
        } else {//未登录状态
            gotoLogin();
            switchFragment(currentTabIndex);
        }
    }


    @Override
    public void setupInitialise() {
        super.setupInitialise();
        mDiscoveryFragment = null;
        mPregnancyFragment = null;
    }


    /**
     * 通过type获取fragment
     *
     * @param type 类型
     * @param bottomMenu 底部菜单
     */
    @Override
    public Fragment getFragmentByType(String type, BottomMenu bottomMenu) {
        if (TextUtils.isEmpty(type)) {
            return null;
        }
        switch (type) {
            case BottomMenuType.HOME:
                return mHomeFragment;
            case BottomMenuType.WAP:
                return new EmtryFragment();
            case BottomMenuType.TOOL:
                return mDiscoveryFragment;
            case BottomMenuType.MALL:
                return new EmtryFragment();

            case BottomMenuType.CALENDAR:
                return mCalendarFragment;
            case BottomMenuType.THREAD:
                return mTopicFragment;
            case BottomMenuType.XJ:
                return mBabyRecordFragment;
            case BottomMenuType.PREGNANCY:
                if (mPregnancyFragment == null) {
                    mPregnancyFragment = PregnancyFragment.Companion.newInstance(
                        UserInfo.instance().getBBid(), false);
                }
                return mPregnancyFragment;
            default:

        }
        return null;
    }

    @Override
    protected int getDynamicHomeIndex() {
        if (homeTabIndex != -1) {
            return homeTabIndex;
        }
        return super.getDynamicHomeIndex();
    }

    @Override
    protected void getHomeDrawerData(boolean registerReportEvent) {
        super.getHomeDrawerData(registerReportEvent);
        mHomeDrawerView.refreshData(registerReportEvent);
    }

    /**
     * 初始化fragment 育儿，有学院没记录 怀孕，有记录没学院
     */
    @Override
    public void initFragment() {
        super.initFragment();
        //首页
        mHomeFragment = HomeFragment.newInstance();

        //发现
        mDiscoveryFragment = DiscoveryFragment.newInstance();
        //圈子
        mTopicFragment = NewTopicFragment.newInstance();
        //日历
        mCalendarFragment = CalendarFragment.newInstance(false);
        //小记信息流
        mBabyRecordFragment = BabyRecordFragment.Companion.newInstance(0,
            UserInfo.instance().getBBid());
    }

    @Override
    protected boolean getHomeMode() {
        return UserInfo.instance().isBaba();
    }


    public void onEventMainThread(final HomePageChangedEvent event) {
        onHomePageChangedEventd(event);
        synchronized (this) {
            switch (event.getIndex()) {
                case HomeConstant.RECORD:
                    toRecord();
                    break;
                case HomeConstant.TO_BABYRECORD:
                    showBabyRecordType(event.getBid());
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 切换积极备孕弹窗
     *
     * @param event 事件
     */
    public void onEventMainThread(PregnancyModeEvent event) {
        if (!this.isFinishing()) {
            new PositivePregnancyDialog(this, event.getTitle(), event.getDesc(),
                event.getPic()).show();
        }
    }

    /**
     * 登录
     */
    private void gotoLogin() {
        startActivityForResult(new Intent(this, LoginActivity.class), RESULT_LOGIN);
    }


    @Override
    public void onFeedChanged(int feed) {

    }

    @Override
    public void onNotifyChanged(int notifyCount) {

    }

    @Override
    public void onPmChanged(int pmCount) {

    }


    @Override
    public void onRequestChanged(int requestCount) {

    }

    @Override
    protected void setCircleRefresh() {
        if (mTopicFragment != null) {
            mTopicFragment.onRefreshCurrentFragment();
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mBabyRecordFragment != null && mBabyRecordFragment.isVisible()) {
            mBabyRecordFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (mPregnancyFragment != null && mPregnancyFragment.isVisible()) {
            mPregnancyFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    protected void scrollToTopOnTab() {
        super.scrollToTopOnTab();
        if (mHomeFragment instanceof IHomeFragment) {
            ((IHomeFragment) mHomeFragment).scrollToTopForRefresh();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (getPresenter() != null) {
            getPresenter().onResume(this);
        }
    }

    @Override
    public void onNewIntentCalendar(int tabIndex, HomeExtra homeExtra) {
        switchFragmentByHomeExtra(tabIndex, homeExtra);
    }


}
