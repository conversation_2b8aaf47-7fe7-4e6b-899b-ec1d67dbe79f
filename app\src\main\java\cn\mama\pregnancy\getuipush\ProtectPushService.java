package cn.mama.pregnancy.getuipush;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import com.igexin.sdk.GTServiceManager;

/**
 * 描述：添加类的描述
 *
 * <AUTHOR>
 * @date 2024/4/24 0024
 */
public class ProtectPushService extends Service {
    @Override
    public IBinder onBind(Intent intent) {
        GTServiceManager.getInstance().onServiceCreate(this,intent);
        return null;
    }
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        GTServiceManager.getInstance().onServiceCreate(this,intent);
        return Service.START_NOT_STICKY;
    }
}
