package cn.mama.pregnant.module.calendar.activity

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import base.mvp.factory.CreatePresenter
import cn.mama.pregnant.business.base.BaseBindingMvpActivity
import cn.mama.pregnant.business.bean.calendar.CalendarDataBean
import cn.mama.pregnant.business.bean.calendar.TemperatureServerBean
import cn.mama.pregnant.business.calendar.GatherContract
import cn.mama.pregnant.business.consts.IntentHelpConsts
import cn.mama.pregnant.business.event.MenstrualChangedEvent
import cn.mama.pregnant.business.event.OvulationModifyEvent
import cn.mama.pregnant.business.router.RouteServiceManager
import cn.mama.pregnant.business.scheme.constants.RouterPathConstants
import cn.mama.pregnant.business.util.CalendarHelper
import cn.mama.pregnant.business.util.NewTimeFormatUtil
import cn.mama.pregnant.business.view.CommonDialogBuilder
import cn.mama.pregnant.business.view.DialogClick
import cn.mama.pregnant.business.view.MultiItemTypeAdapter
import cn.mama.pregnant.business.view.RecyclerViewAdapter
import cn.mama.pregnant.business.view.textview.DiffTypefaceTextGroup
import cn.mama.pregnant.databinding.ActivityMenstrualReportBinding
import cn.mama.pregnant.module.calendar.constant.MenstrualReportContract
import cn.mama.pregnant.module.calendar.itemview.CollectFolicAcidView
import cn.mama.pregnant.module.calendar.itemview.CollectItemType
import cn.mama.pregnant.module.calendar.itemview.CollectMakeLoveView
import cn.mama.pregnant.module.calendar.itemview.CollectMenstrualPeriodView
import cn.mama.pregnant.module.calendar.itemview.CollectOvulationStripView
import cn.mama.pregnant.module.calendar.itemview.CollectTemperatureView
import cn.mama.pregnant.module.calendar.itemview.CollectWhiteBandView
import cn.mama.pregnant.module.calendar.itemview.MenstrualAnalysisItemView
import cn.mama.pregnant.module.calendar.itemview.MoonRecordDelayItemView
import cn.mama.pregnant.module.calendar.presenter.MenstrualReportPresenter
import cn.mama.pregnant.widget.view.recycleview.bean.RecyclerViewBean
import com.alibaba.android.arouter.facade.annotation.Route
import util.*

/**
 * 经期记录汇总
 *
 * <AUTHOR>
 * @date 2021/9/2
 */
@Route(path = RouterPathConstants.MENSTRUAL_REPORT)
@CreatePresenter(MenstrualReportPresenter::class)
class MenstrualReportActivity :
    BaseBindingMvpActivity<ActivityMenstrualReportBinding, MenstrualReportContract.View, MenstrualReportContract.Presenter>(),
    MenstrualReportContract.View {

    companion object {
        @JvmStatic
        fun launch(context: Context) {
            if (RouteServiceManager.getInstance().accountProvider?.login(context) == true) {
                val intent = Intent(context, MenstrualReportActivity::class.java)
                intent.addNewTaskFlagIfNeed(context)
                context.startActivity(intent)
            }
        }
    }

    /**
     * 板块列表数据
     */
    private var collectList: MutableList<RecyclerViewBean> = mutableListOf()
    private var mRecyclerViewAdapter: RecyclerViewAdapter<RecyclerViewBean>? = null

    private val tgWeightFirstInfo by lazy { DiffTypefaceTextGroup(this) }

    /**
     * 跳去经期记录页面
     */
    private val  resultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            presenter.fetchData()
        }
    }

    override fun enableEventBus() = true

    override fun initView(savedInstanceState: Bundle?) {
        setStatusBarColorRes(cn.mama.pregnant.business.R.color.translucent)
        // 初始化记录汇总列表
        initRecordCollect()

        tgWeightFirstInfo.showColon = false
        tgWeightFirstInfo.setText(null, null)
    }


    override fun initData(savedInstanceState: Bundle?) {
        presenter.fetchData()
    }

    override fun initListener() {
        binding.titleBar.setOnBackListener { handleBackPressed() }
    }

    override fun showEmptyMenstrualDialog() {
        CommonDialogBuilder()
            .setPositiveText(getString(cn.mama.pregnant.business.R.string.ok))
            .setPassiveText(getString(cn.mama.pregnant.business.R.string.cancel))
            .setCancelable(false)
            .setContent("记录大姨妈后，才能分周期看记录，快点击日历开始记大姨妈吧！")
            .setListener(object : DialogClick {
                override fun onSureClick(dialog: AlertDialog) {
                    dialog.dismiss()
                    gotoCalendar()
                }

                override fun onCancelClick(dialog: AlertDialog) {
                    dialog.dismiss()
                }
            })
            .build(this)
            .show()
    }

    override fun gotoPeriodList() {
        PeriodListActivity.launch(this)?.let {
            resultLauncher.launch(it)
        }
    }

    override fun gotoTemperatureChart() {
        RouteServiceManager.getInstance().adultThermometerProvide?.goTemperatureActivity(this)
    }

    override fun gotoOvulationTestStrip() {
        RouteServiceManager.getInstance().ovulationProvider?.gotoOvulationTestStrip(this)
    }

    override fun gotoMakeLove() {
        MakeLoveRecordActivity.launch(this)
    }

    override fun gotoWhites() {
        LeucorrheaRecordActivity.launch(this)
    }

    override fun goToCommonWeightChart() {
        RouteServiceManager.getInstance()
            .babyRecordProvider
            ?.launchCommonWeightChart(this)
    }

    override fun gotoFolicAcidDetails() {
        FolicAcidDetailsActivity.launch(this)
    }

    private fun gotoCalendar() {
        val intent = Intent().putExtra(IntentHelpConsts.INTENT_TYPE, IntentHelpConsts.TYPE_CALENDAR)
        RouteServiceManager.getInstance().mainPublicProvider?.intentHelper(this,intent)
    }

    fun onEventMainThread(event: CalendarDataBean) {
        presenter.fetchData()
    }

    fun onEventMainThread(event: OvulationModifyEvent) {
        presenter.fetchData()
    }

    /**
     * 经期设置发生变化
     */
    fun onEventMainThread(event: MenstrualChangedEvent) {
        presenter.fetchData()
    }

    override fun enableBackPressedDispatcher(): Boolean {
        return true
    }

    /**
     * 初始化记录汇总列表
     * */
    private fun initRecordCollect(){
        buildFakeData()

        binding.rvRecordCollect.layoutManager = LinearLayoutManager(this)
        binding.rvRecordCollect.isNestedScrollingEnabled = true
        val adapter = MultiItemTypeAdapter(this, collectList)
        //经期记录
        adapter.addItemViewDelegate(CollectMenstrualPeriodView(lifecycle, this){
            presenter.handlePeriodBlockClickEvent()
        })
        //体温
        adapter.addItemViewDelegate(CollectTemperatureView(lifecycle, this){
            when(it){
                GatherContract.SLIDE_BLOCK -> presenter.handleTemperatureBlockClickEvent()
                GatherContract.SLIDE_LEFT -> presenter.fetchTemperatureChartData(it)
                GatherContract.SLIDE_RIGHT -> presenter.fetchTemperatureChartData(it)
            }
        })
        //排卵试纸
        adapter.addItemViewDelegate(CollectOvulationStripView(lifecycle, this){
            presenter.handleOvulationTestStripBlockClickEvent()
        })
        //同房
        adapter.addItemViewDelegate(CollectMakeLoveView(lifecycle, this){
            presenter.handleMakeLoveBlockClickEvent()
        })
        //白带
        adapter.addItemViewDelegate(CollectWhiteBandView(lifecycle, this){
            presenter.handleWhitesBlockClickEvent()
        })
        //补叶酸
        adapter.addItemViewDelegate(CollectFolicAcidView(lifecycle, this){
            presenter.handleFolicAcidBlockClickEvent()
        })
        //经期推迟原因
        adapter.addItemViewDelegate(MoonRecordDelayItemView(lifecycle))
        //经期分析
        adapter.addItemViewDelegate(MenstrualAnalysisItemView(lifecycle){
            presenter.handlePeriodBlockClickEvent()
        })

        mRecyclerViewAdapter = RecyclerViewAdapter(adapter)
        binding.rvRecordCollect.adapter = mRecyclerViewAdapter
    }


    override fun refreshTotalData(dataList: MutableList<RecyclerViewBean>) {
        collectList.clear()
        collectList.addAll(dataList)
        mRecyclerViewAdapter?.notifyDataSetChanged()
    }


    override fun refreshTemperatureInfo(temperature: TemperatureServerBean?) {
        collectList.forEach {
            if(it.type == CollectItemType.COLLECT_TEMPERATURE){
                it.data = temperature
            }
        }
        mRecyclerViewAdapter?.notifyDataSetChanged()
    }


    private fun buildFakeData(){
        collectList.clear()
        // 构建1个月的假数据
        val pattern = NewTimeFormatUtil.FORMAT_YYYY_MM_DD_POINT
        val startDate = CalendarHelper.getMenStartDate()
        val endDate = startDate.plusMonths(1)
        val temperature = TemperatureServerBean()

        temperature.startDate = NewTimeFormatUtil.formatTime(NewTimeFormatUtil.FORMAT_YYYY_MM_DD,startDate.toDate())
        temperature.endDate = NewTimeFormatUtil.formatTime(NewTimeFormatUtil.FORMAT_YYYY_MM_DD,endDate.toDate())
        temperature.stageDate = "${startDate.toString(pattern)}~${endDate.toString(pattern)}"

        collectList.add(RecyclerViewBean(
            CollectItemType.COLLECT_TEMPERATURE,
            temperature,
        ))
    }


}