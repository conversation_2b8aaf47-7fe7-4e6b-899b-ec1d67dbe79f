package cn.mama.pregnant.module.calendar.constant;

import static cn.mama.pregnant.business.consts.UrlPath.PathHeadNew;

/**
 * <AUTHOR>
 */
public class UrlPath {

    /**
     * 获取同房提醒
     */
    public static final String GET_MAKE_LOVE_REMIND =
        PathHeadNew + "api/makelove/getMakeloveRemind";

    /**
     * 记经期-记录汇总
     */
    public static final String GET_MENSTRUAL_RECORD_TOTAL = PathHeadNew + "api/preg_menstrual/recordTotal";

    /**
     * 【11.12.0】检查日期能否保存排卵日
     */
    public static final String CHECK_OVULATION = PathHeadNew + "api/preg_menstrual/check_ovulation";

    /***
     * 【11.12.0】保存排卵日
     */
    public static final String SAVE_OVULATION = PathHeadNew + "api/preg_menstrual/save_ovulation";

    /**
     * 7.5.0 备孕首页大姨妈开关
     */
    public static final String MENSTRUAL_HOMEPAGESAVE = PathHeadNew + "api/preg_menstrual/home_page_save";

    /**
     * 7.5.0 保存同房接口
     */
    public static final String SAVE_MAKELOVE = PathHeadNew + "api/makelove/save_makelove";


    /**
     * 7.5.0 保存心情接口
     */
    public static final String SAVE_MOOD = PathHeadNew + "api/mood/save_mood";

    /**
     * 7.5.0 保存白带接口
     */
    public static final String SAVE_LEUCORRHEA = PathHeadNew + "api/leucorrhea/save_leucorrhea";


    /**
     * 7.5.0 备孕工具初始化数据
     */
    public static final String CALENDAR_GET_CALENDAR_TOOL_CONF = PathHeadNew + "api/calendar/get_calendar_tool_conf";


    /**
     * 获取好孕率
     */
    public static final String GET_PREGNANCY_PERCENT = PathHeadNew + "api/preg_menstrual/get_pregnancy_percent";

    /**
     * 7.1.2 姨妈日历提示
     */
    public static final String GET_DATA_BY_MONTHTIPS = PathHeadNew + "api/preg_menstrual/get_tips";

    /**
     * 6.6 大姨妈日历
     */
    public static final String GET_DATA_BY_MONTH = PathHeadNew + "api/preg_menstrual/get_data_by_month";


    /**
     * 7.5.0 备孕日历每月工具数据
     */
    public static final String CALENDAR_GET_LIST_BY_MONTH = PathHeadNew + "api/calendar/get_list_by_month";

    /**
     * 7.5.0 获取体温小知识url
     */
    public static  String TEMPERATURE_DETAIL_URL = "https://papi.mama.cn/wap/pre_knowledge/share?id=22";

    /**
     * 7.5.0 获取白带小知识url
     */
    public static String LEUCORRHEA_DETAIL_URL = "https://papi.mama.cn/wap/pre_knowledge/share?id=226";


    /**
     * 11.10 记录叶酸
     */
    public static final String SAVE_FOLIC_ACID = PathHeadNew + "api/folic_acid/save";

    /**
     * 12.6.0
     * 获取备孕记经期帖子弹窗
     */
    public static final String GET_MENSTRUAL_POST_DIALOG = PathHeadNew + "api/new_record/get_record_popup";

    /**
     * 备孕日历工具配置
     */
    public static final String GET_CALENDAR_TOOL_LIST = PathHeadNew + "api/calendar/get_calendar_tool_list";
}
