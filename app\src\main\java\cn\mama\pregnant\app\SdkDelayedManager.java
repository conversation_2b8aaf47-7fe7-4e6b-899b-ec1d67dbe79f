package cn.mama.pregnant.app;

import android.content.Context;

import com.lzx.musiclib.manager.MusicManager;

import cn.mama.jssdk.util.WxMiniUtil;
import cn.mama.pregnant.business.consts.Constants;

/**
 * <AUTHOR>
 * 将一些不重要的SDK初始化延后
 */
public class SdkDelayedManager {

    /**
     * 初始化其他
     * @param context 上下文
     */
    public static void  initOther(Context context){
        WxMiniUtil.setAppId(context.getString(cn.mama.pregnant.mamaloginshare.R.string.weixin_id));
    }



    /**
     * 音频服务
     */
    public static void initMusicLibrary(Context context) {
        MusicManager.get().init(context, Constants.APPPAGKENAME);
    }


}
