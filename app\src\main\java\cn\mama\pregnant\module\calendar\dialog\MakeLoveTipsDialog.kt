package cn.mama.pregnant.module.calendar.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import cn.mama.pregnant.R
import cn.mama.pregnant.business.bean.calendar.MakeLoveTips
import cn.mama.pregnant.business.consts.MenstrualCycle
import cn.mama.pregnant.business.util.reportImpressionEvent
import cn.mama.pregnant.module.calendar.utils.ExposureSp
import java.util.Date

/**
 * 经期同房提示
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
class MakeLoveTipsDialog(
    context: Context,
    private val tips: MakeLoveTips,
) : Dialog(context, com.mama.babyrecord.R.style.CalendarDialog) {

    companion object {

        @JvmStatic
        fun showIfNeed(
            context: Context,
            tips: MakeLoveTips,
            stage: Int,
            action: () -> Unit,
        ) {
            val exposureSp = ExposureSp.getInstance()
            if (stage == MenstrualCycle.MENSTRUAL && exposureSp.shouldShowMakeLoveTips()) {
                exposureSp.saveMakeLoveTips(Date())
                try {
                    MakeLoveTipsDialog(context, tips)
                        .apply {
                            setOnDismissListener { action.invoke() }
                        }
                        .show()
                } catch (e: Exception) {
                    e.printStackTrace()
                    action.invoke()
                }
            } else {
                action.invoke()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_make_love_tips)
        setCanceledOnTouchOutside(true)
        window?.let {
            it.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            it.setBackgroundDrawableResource(android.R.color.transparent)
        }

        findViewById<TextView>(R.id.tv_title).text = tips.title
        findViewById<TextView>(R.id.tv_content).text = tips.content
        findViewById<View>(R.id.tv_ok).setOnClickListener { dismiss() }

        tips.reportEvent?.reportImpressionEvent()
    }

}