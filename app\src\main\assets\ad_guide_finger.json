{"v": "5.6.10", "fr": 25, "ip": 0, "op": 15, "w": 150, "h": 150, "nm": "合成 1", "ddd": 0, "assets": [{"id": "image_0", "w": 77, "h": 83, "u": "images/", "p": "img_0.png", "e": 0}, {"id": "image_1", "w": 48, "h": 48, "u": "images/", "p": "img_1.png", "e": 0}, {"id": "image_2", "w": 69, "h": 69, "u": "images/", "p": "img_2.png", "e": 0}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "手.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [43, 64, 0], "ix": 2}, "a": {"a": 0, "k": [0.5, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 8, "s": [75, 75, 100]}, {"t": 15, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\n$bm_rt = loopIn('cycle', 0);"}}, "ao": 0, "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "小圆.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [77]}, {"t": 15, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46.25, 69.5, 0], "ix": 2}, "a": {"a": 0, "k": [24, 24, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 8, "s": [90, 90, 100]}, {"t": 15, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\n$bm_rt = loopIn('cycle', 0);"}}, "ao": 0, "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "大圆.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [88]}, {"t": 15, "s": [88]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46.25, 69.5, 0], "ix": 2}, "a": {"a": 0, "k": [34.5, 34.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 8, "s": [89, 89, 100]}, {"t": 15, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\n$bm_rt = loopIn('cycle', 0);"}}, "ao": 0, "ip": 0, "op": 50, "st": 0, "bm": 0}], "markers": []}