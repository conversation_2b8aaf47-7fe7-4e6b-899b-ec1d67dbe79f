package cn.mama.pregnancy.getuipush;

import android.app.Activity;
import android.os.Bundle;
import com.igexin.sdk.GTServiceManager;

/**
 * 守护个推的类
 */
public class ProtectPushActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //调⽤个推GTServiceManager的onActivityCreate⽅法。必须传递this
        GTServiceManager.getInstance().onActivityCreate(this);
    }
}